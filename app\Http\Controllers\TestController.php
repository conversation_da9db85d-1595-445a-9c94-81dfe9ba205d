<?php

namespace App\Http\Controllers;

use App\Models\AgnetReport;
use App\Models\Cdr;
use App\Models\Inbound;
use App\Models\IVR;
use App\Models\Permission;
use App\Models\Queue;
use App\Models\SystemSetting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Laratrust\Helper;
use Larinfo;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Action\QueueAddAction;
use PAMI\Message\Action\QueueLogAction;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Event\CoreShowChannelEvent;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;
use Setting;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Http;
class TestController extends Controller
{
    public function get_agent_status(Request $request)
    {
        $user = User::query()->findOrFail($request->user);
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to fetch data.";
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                return response()->json($response->getEvents()[1] instanceof QueueMemberEvent);
            } else {
                return response()->json("Failed to fetch agent status", 500);
            }
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function test(Request $request)
    {
        $request->validate([
            "date" => ["required", "date"]
        ]);
        try {
            $query1 = "select DISTINCT c.src from queue_log q join cdr c on q.callid = c.uniqueid where q.Event = 'ABANDON' and date(q.time) = {$request->date}";
            $query2 = "select DISTINCT q.callid from queue_log q where q.Event = 'ABANDON' and date(q.time) = {$request->date}";
            $qMain1 = "select c.src as number, c.start as c_time, c.accountcode from cdr c where c.src in ($query1) and c.uniqueid not in ($query2) and date(c.start) = {$request->date}";
            $qMain2 = "select c.dst as number, c.start as c_time, c.accountcode from cdr c where c.dst in ($query1) and c.uniqueid not in ($query2) and date(c.start) = {$request->date}";
            $query = "$qMain1 UNION $qMain2";
            $abandoned = "select q.Event, q.data3, q.callid, c.src as number, c.start, c.disposition, c.dstchannel from queue_log q join cdr c on q.callid = c.uniqueid where q.Event = 'ABANDON' and date(q.time) = {$request->date}";
            dd($abandoned);
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    private function getOptions(): array
    {
        return [
            'host' => '***************',
            'scheme' => 'tcp://',
            'port' => '5038',
            'username' => 'defaultapp',
            'secret' => 'randomsecretstring',
            'connect_timeout' => 1000,
            'read_timeout' => 1000
        ];
    }

    private function queueParser(array $data): array
    {
        // Only allowed keys should be returned
        $allowed = ['queue', 'max', 'strategy', 'calls', 'holdtime', 'talktime', 'completed', 'abandoned', 'servicelevelperf2', 'weight'];
        $nData = [];

        foreach ($allowed as $item) {
            $nData[$item === 'servicelevelperf2' ? 'Service Level' : ucfirst($item)] = $data[$item];
        }

        return $nData;
    }

    private function agentParser(array $data): array
    {
        $allowed = ['name', 'stateinterface', 'callstaken', 'lastcall', 'lastpause', 'incall', 'status', 'paused', 'pausedreason', 'wrapuptime'];
        $nData = [];

        foreach ($allowed as $item) {
            if ($item === 'stateinterface')
                $nData['Interface'] = explode("/", $data[$item])[1];
            elseif ($item === 'lastcall' || $item === 'lastpause')
                $nData[ucfirst($item)] = Carbon::createFromTimestamp($data[$item])->diffForHumans();
            elseif ($item === 'paused' || $item === 'incall')
                $nData[ucfirst($item)] = $data[$item] === '1' ? 'Yes' : 'No';
            elseif ($item === 'status')
                $nData[ucfirst($item)] = $this->mapState($data[$item]);
            else
                $nData[ucfirst($item)] = $data[$item];
        }

        return $nData;
    }

    /**
    Status - The numeric device state status of the queue member.
    0 - AST_DEVICE_UNKNOWN
    1 - AST_DEVICE_NOT_INUSE
    2 - AST_DEVICE_INUSE
    3 - AST_DEVICE_BUSY
    4 - AST_DEVICE_INVALID
    5 - AST_DEVICE_UNAVAILABLE
    6 - AST_DEVICE_RINGING
    7 - AST_DEVICE_RINGINUSE
    8 - AST_DEVICE_ONHOLD
     */
    private function mapState(int $state)
    {
        $state = (int) $state;
        switch ($state) {
            default:
                return $state;
            case 0:
                return 'DEVICE_UNKNOWN';
            case 1:
                return 'DEVICE_NOT_INUSE';
            case 2:
                return 'DEVICE_INUSE';
            case 3:
                return 'DEVICE_BUSY';
            case 4:
                return 'DEVICE_INVALID';
            case 5:
                return 'DEVICE_UNAVAILABLE';
            case 6:
                return 'DEVICE_RINGING';
            case 7:
                return 'DEVICE_RINGINUSE';
            case 8:
                return 'DEVICE_ONHOLD';

        }
    }


    public function getAccessToken()
    {
        // Static client credentials
        $clientId = 'm5YqAmnlen9Ld44UItTAJA..';
        $clientSecret = '1xbguNS3EM5ehlzTyZ4stQ..';

        $basicToken = base64_encode($clientId . ':' . $clientSecret);
        $response = Http::withoutVerifying()
            ->withHeaders([
                'Authorization' => 'Basic ' . $basicToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])
            ->asForm()
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/oauth/token', [
                'grant_type' => 'client_credentials',
            ]);
        if ($response->successful()) {
            return $response->json()['access_token'];
        } else {
            // You can log the error for debugging
            \Log::error('Token retrieval failed', ['response' => $response->body()]);
            return null;
        }
    }
     public function getApiKey()
    {
        $token = $this->getAccessToken();
        $response = Http::withoutVerifying()
            ->withToken($token)
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/get_api_key/', [
                'username' => 'MPCCS',
            ]);

        if ($response->successful()) {
            return $response->json(['API Key']);
        } else {
            \Log::error('API Key retrieval failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            return null;
        }
    }

    public function agentStatus()
    {
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();
        $response = Http::withToken($token)->
            withoutVerifying()
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/agent_login_status', [
                'AgentID' => "146",
                'AgentApiKey' => $api_key
            ]);
        return $response->json();

        // $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
        // $this->info(" Response Message: {$result['Response Message']}, Date/Time:{$result['Date/Time']}, Memory Usage: {$mem_usage} ");
        // unset($result, $response, $api_key);
    }

    public function newstartcall(Request $request)
    {
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();
        $response = Http::withToken($token)->withoutVerifying()->
            post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/startcall/', [
                'AgentID' => $request->AgentID,
                'LeadID' => $request->LeadID,
                'CallType' => $request->CallType,
                'AgentApiKey' => $api_key,
            ]);
        return $response->json();
        // return $response->json(['Call ID']);
    }

    public function newendcall(Request $request)
    {
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();
        $response = Http::withToken($token)->withoutVerifying()
            ->put('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/endcall/', [
                'AgentID' => $request->AgentID,
                'CallID' => $request->CallID,
                'EndCallFeedback' => $request->EndCallFeedback,
                'AgentApiKey' => $api_key
            ]);
        return $response->json();
        // return $response->json(['Call ID']);

    }






}
