<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class RingNoAnswerAgentController extends Controller
{
    public function getData_backup(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
         * from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
         * where a.Event = 'RINGNOANSWER' group by a.Agent
         */

        $date = [];
        $filter = "";

        if (isset($request->date)) {
            $date[0] = Carbon::parse($request->date[0])->toDateString();
            $date[1] = Carbon::parse($request->date[1])->toDateString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
        } else {
            $date[0] = Carbon::now()->toDateString();
            $filter .= "and date(a.time) = '{$date[0]}'";
        }

        if (isset($request->agents) && is_array($request->agents)) {
            $agents = implode("','", $request->agents);
            $filter .= " and a.Agent IN ('{$agents}')";
        }

        try {
            $data = DB::select("SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
                            from queue_log a
                            join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
                            where a.Event = 'RINGNOANSWER' {$filter}
                            group by a.Agent");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getData(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
         * from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
         * where a.Event = 'RINGNOANSWER' group by a.Agent
         */

        $date = [];
        $filter = "";

        if (isset($request->date)) {
            $date[0] = Carbon::parse($request->date[0])->toDateString();
            $date[1] = Carbon::parse($request->date[1])->toDateString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
        } else {
            $date[0] = Carbon::now()->toDateString();
            $filter .= "and date(a.time) = '{$date[0]}'";
        }

        if (isset($request->agent) && is_array($request->agent)) {
            $agents = implode("','", $request->agent);
            $filter .= " and a.Agent IN ('{$agents}')";
        }

        if(isset($request->queue) && !empty($request->queue)){
            $queueName = trim($request->queue);
            $filter .= " and a.queuename LIKE '%{$queueName}%'";
        }
        else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            $queues = implode("','", $queues);
            $filter .= " and a.queuename IN ('{$queues}')";
        }

        try {
            $data = DB::select("SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
                            from queue_log a
                            join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
                            where a.Event = 'RINGNOANSWER' {$filter}
                            group by a.Agent");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

}
