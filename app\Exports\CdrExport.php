<?php

namespace App\Exports;

use App\Models\Cdr;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;


class CdrExport implements FromQuery, WithHeadings, WithMapping

{
    /**
    * @return \Illuminate\Support\Collection
    */

   public function query(){

        //dd($input);
        $date = Carbon::now();
            $data = Cdr::query()
            ->select(['users.name','uniqueid', 'work_codes.name as workcode', 'recordingfile', 'accountcode', 'src', 'dst', 'channel', 'dstchannel', 'disposition', 'duration','transcription', 'start', 'end'])
            ->leftJoin("users", function ($join){
                $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
            ->leftJoin('queue_log', function ($join) {
                    $join->on('queue_log.callid', '=', 'cdr.uniqueid')
                        ->where('queue_log.Event', '=', 'Workcode');
                })
                ->leftJoin('work_codes', 'work_codes.id', '=', 'queue_log.data1')
                ->where('lastapp', '!=', 'AGI')
            ->whereDate('start', '>=', $date->startOfDay())
            ->whereDate('end', '<=', $date->endOfDay())
            ->orderBy('start', 'desc');
       // dd($data);
        return $data;
   }

    public function map($data): array{
        
        return[
            $data['name'],
            $data['uniqueid'],
            $data['recordingfile'],
            $data['accountcode'],
            $data['src'] ,
            $data['dst'] ,
            $data['channel'],
            $data['dstchannel'],
            $data['disposition'],
            $data['duration'],
            $data['transcription'],
            $data['workcode'],
            $data['start'],
            $data['end']
        ];
    }

    public function headings() :array{

        return[
            'Name',
            'Unique ID',
            'Recording File Name',
            'Call Type',
            'Source',
            'Destination',
            'Channel',
            'Dst Channel',
            'Call Status',
            'Duration',
            'Transcription',
            'Disposition(workcode)',
            'Start',
            'End',
        ];
    }
}
