<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Carbon\Carbon;

class RingNoAnswerExport implements FromArray, WithMapping, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
   public function __construct(array $data) {

        $this->data = $data;
   }

   public function array(): array {

        return $this->data;
   }

   public function map($data): array {

        return[
            $data->queue,
            $data->partya,
            $data->agent,
            Carbon::parse($data->time)->format('d/m/Y h:i:s A')
        ];
   }

   public function headings(): array {

        return[
            'Queue',
            'Party A',
            'Agent',
            'Time'
        ];
   }
}
