<?php

namespace App\Exports;

use App\Models\Cdr;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class CdrFilterExport implements FromArray, WithHeadings, WithMapping

{
    /**
     * @return \Illuminate\Support\Collection
     */

    protected $data;
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    public function array(): array
    {
        return $this->data;
    }

    // public function map($data): array{

    //     return[
    //         $data->name,
    //         $data->uniqueid,
    //         $data->recordingfile,
    //         $data->accountcode,
    //         $data->src,
    //         $data->dst ,
    //         $data->channel,
    //         $data->dstchannel,
    //         $data->disposition,
    //         $data->duration,
    //         $data->start,
    //         $data->end
    //     ];
    // }


    public function map($data): array
    {
        return [
            $data['name'],
            $data['uniqueid'],
            $data['recordingfile'],
            $data['accountcode'],
            $data['src'],
            $data['dst'],
            $data['channel'],
            $data['dstchannel'],
            $data['disposition'],
            $data['duration'],
            $data['transcription'],
            $data['workcode'],
            $data['start'],
            $data['end']
        ];
    }

    public function headings(): array
    {

        return [
            'Name',
            'Unique ID',
            'Recording File Name',
            'Call Type',
            'Source',
            'Destination',
            'Channel',
            'Dst Channel',
            'Call Status',
            'Duration',
            'Transcription',
            'Disposition(workcode)',
            'Start',
            'End',
        ];
    }
}
