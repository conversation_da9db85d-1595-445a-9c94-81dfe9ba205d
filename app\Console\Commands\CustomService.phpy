<?php

namespace App\Console\Commands;

use App\Http\Traits\BitrixIntegrationTrait;
use App\Models\Cdr;
use App\Models\ServiceRating;
use App\Models\ServiceRatingSetting;
use App\Models\SystemSetting;
use App\Models\VoicemailSetting;
use Carbon\Carbon;
use Illuminate\Console\Command;
use PAGI\CDR\Impl\CDRFacade;
use PAGI\Exception\ChannelDownException;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\MixMonitorAction;
use PAMI\Message\Action\PJSIPShowEndpointAction;
use PAMI\Message\Action\RedirectAction;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\QueueStatusAction;
use App\Models\User;
use DB;
use Illuminate\Support\Facades\Http;

class CustomService extends Command
{
    use BitrixIntegrationTrait;
    public int $responseWaitTime = 10000; // In milliseconds
    public array $responseArray = [1, 2];
    public int $responseTries = 3;
    public string $capturedDigits = '';
    public string $recording = 'trax/for_rating';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'custom-services:start';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'The command will start a daemon that will listen to custom agi services.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    private function removeZero($callerId)
    {
        if($callerId[0] == '0') {
            return ltrim($callerId, '0');
        }
        return $callerId;
    }

    private function cleanChannel($channel)
    {
        return explode('-', explode('/', $channel)[1])[0];
    }

    private function getAgent($callerId)
    {
        // Remove leading zero first
        $callerId = $this->removeZero($callerId);
        $cdr = Cdr::query()->select(['dstchannel'])->where('accountcode', 'Queue')->where('lastapp', 'Queue')->where('src', 'like', "%$callerId%")->where('disposition', 'ANSWERED')->orderBy('start', 'desc')->first();
        return $cdr->dstchannel ?? '';
    }

    private function getAgentForServiceRating(CDRFacade $CDRFacade)
    {
        return Cdr::query()->where('uniqueid', $CDRFacade->getUniqueId())->orderBy('start', 'desc')->first();
    }

    private function checkTimings(): bool
    {
        $settings = VoicemailSetting::query()->first();
        $now = Carbon::now();

        if(!$settings->status)
            return false;

        // Step1 - compute special days
        if(in_array($now->format('Y-m-d'), $settings->specificDates)) {
            if($settings->specificDateStart && $settings->specificDateEnd && $now->format('H:i:s') >= $settings->specificDateStart && $now->format('H:i:s') <= $settings->specificDateEnd)
                return true;
        }

        // Step2 - compute week
        if(in_array($now->dayName, $settings->except))
            return true;
        elseif(in_array($now->dayName, $settings->weeks)) {
            // Step3 - compute timings
            $start = Carbon::parse($settings->start);
            $end = Carbon::parse($settings->end);
            if($now->between($start, $end))
                return true;
        }
        return false;
    }

    public function getAccessToken()
    {
        // Static client credentials
        $clientId = 'm5YqAmnlen9Ld44UItTAJA..';
        $clientSecret = '1xbguNS3EM5ehlzTyZ4stQ..';

        $basicToken = base64_encode($clientId . ':' . $clientSecret);
        $response = Http::withoutVerifying()
            ->withHeaders([
                'Authorization' => 'Basic ' . $basicToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])
            ->asForm()
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/oauth/token', [
                'grant_type' => 'client_credentials',
            ]);
        if ($response->successful()) {
            return $response->json()['access_token'];
        } else {
            // You can log the error for debugging
            \Log::error('Token retrieval failed', ['response' => $response->body()]);
            return null;
        }
    }

    public function getApiKey()
    {
        $token = $this->getAccessToken();
        $response = Http::withoutVerifying()
            ->withToken($token)
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/get_api_key/', [
                'username' => 'MPCCS',
            ]);

        if ($response->successful()) {
            return $response->json(['API Key']);
        } else {
            \Log::error('API Key retrieval failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            return null;
        }
    }

    public function endCall($userId, $callId, $serviceRating)
    {
	\Log::info("End Call Executed");
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();

        $response = Http::withToken($token)->withoutVerifying()
            ->put('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/endcall/', [
                'AgentID' => "146",
                'CallID' => $callId,
                'EndCallFeedback' => $serviceRating,
                'AgentApiKey' => $api_key

            ]);

        $result = $response->json();
        $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));

	\Log::info("AgentID : 146 && CallID : $callId && EndCallFeedback : $serviceRating && AgentApiKey : $api_key && Token : $token");
	\Log::info(json_encode($result));
        \Log::info(" Call ID: {$result['Call ID']}, Response Message: {$result['Response Message']}, Date/Time:{$result['Call Date/Time']}, Memory Usage: {$mem_usage} ");
        unset($result, $response, $api_key);
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = Carbon::now();
        $this->info("[{$date->toDateTimeString()}] INFO: Starting custom call routing invoke script...");
        $client = new ClientImpl($this->getAMIOptions());
        $client->registerEventListener(function (EventMessage $event) use ($client, $date) {
            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);
            $var = $agiClient->getChannelVariables();
            $arg = $var->getArgument(1);
            $callerId = $var->getCallerId();
            $uniqueId = $var->getUniqueId();
            $date = Carbon::now();
            $this->info("[{$date->toDateTimeString()}] INFO: Incoming call from $callerId");

            if($arg == "IvrMenu") {
                return;
            }

            if($arg === 'custom-routing') {
                //$agent = $this->getAgent($callerId);
                $agent = $this->customCallRouting($callerId); // for bitrix custom-routing in bitrix branch
                if($agent && $agent !== ' ') {
                    $date = Carbon::now();
                    $this->info("[{$date->toDateTimeString()}] INFO: Found agent of previous call for call-routing: $agent");
                    $user = User::where('auth_username', $agent)->first('id');
                    $queue = DB::table('queue_user')->where('user_id', $user->id)->first('queue_name');
                    $action = new PJSIPShowEndpointAction($agent);
                    //checking is ready or not
                    $break = new QueueStatusAction($queue->queue_name, "PJSIP/{$agent}");
                    //
                    try {
                        $response = $client->send($action);
                        $break_response = $client->send($break);
                        if($response->getKey('response') !== 'Error' && $break_response && $break_response->getEvents()[1]->getKey('paused') ==  0 ) {
                            $deviceState = $response->getEvents()[0]->getKey('DeviceState');
                            if($deviceState === "Not in use") {
                                $channel = $var->getChannel();
                                $this->info("{$channel}");
                                $cdr = $agiClient->getCDR();
                                $exten = $cdr->getDestination();
                                $date = Carbon::now();
                                $fileName = "custom_routing-$exten-$callerId-{$date->format('Ymd-His')}-$uniqueId.wav";
                                $fileLoc = "{$date->format('Y')}/{$date->format('m')}/{$date->format('d')}/$fileName";
                                $cdr->setAccountCode("Custom_Routing");
                                $cdr->setCustom('recordingfile', $fileName);
                                $recordAction = new MixMonitorAction($channel);
                                $recordAction->setOptions(['b']);
                                $recordAction->setFile($fileLoc);
                                $action = new RedirectAction($channel, $agent, "default", 1);
                                $response = $client->send($action);
                                $response = $client->send($recordAction);
                                $date = Carbon::now();
                                $this->info("[{$date->toDateTimeString()}] INFO: [$callerId] Call routed to designated user @ {$agent}...");
                            }
                        }
                    } catch (\Exception $exception) {
                        $date = Carbon::now();
                        $this->info("[{$date->toDateTimeString()}] INFO: Exception occurred: {$exception->getMessage()}");
                    }
                }
                $agiClient->asyncBreak();
            } elseif ($arg === 'service-rating') {
                // Service rating code goes here...
                $agiClient = new AsyncClientImpl([
                    'pamiClient' => $client,
                    'asyncAgiEvent' => $event
                ]);
                $tries = $this->responseTries;
                $settings = ServiceRatingSetting::query()->first();
                $file = $settings->serviceRatingFile;
                $this->recording = $file->fileloc;
                $this->responseArray = [$settings->best, $settings->worst];

                try {
                    //$logger = $agiClient->getAsteriskLogger();
                    //$logger->notice('Starting sync execution of call...');
                    $agiClient->answer();
                    $cdr = $agiClient->getCDR();
                    do {
                        $response = $agiClient->getData($this->recording, $this->responseWaitTime, 1);
                        $tries--;
                        if(in_array($response->getDigits(), $this->responseArray)) {
                            break;
                        }
                    } while($tries > 0);
                    //$logger->notice("Response captured from user: {$response->getDigits()}");
                    if($response->getDigits()) {
                        //$agent = $this->getAgentForServiceRating($cdr);
                        $this->capturedDigits = $response->getDigits();
                        $rating = new ServiceRating;
                        $rating->uniqueid = $cdr->getUniqueId();
                        $rating->rating = $response->getDigits();
                        $rating->number = $cdr->getSource();
                        $rating->agentId = "";
                        $rating->save();
                    }
                    $unique_id = $cdr->getUniqueId();
                    $account_code = $cdr->getAccountCode();
                    $agiClient->hangup();
                    sleep(5);
                    $rating_data = ServiceRating::where('uniqueid',  $unique_id)->first();

		    $channel="";

                        if($rating_data && $account_code == 'Queue'){
                            $cdr_data = Cdr::query()->where('uniqueid',$unique_id)->where('accountcode', 'Queue')->where('disposition', 'ANSWERED')->first();
                            $rating_data->agentId = $cdr_data->dstchannel;
			    $channel = $cdr_data->dstchannel;
                            $rating_data->save();
                        }else if($rating_data && $account_code == 'Outbound') {
                            $cdr_linked = Cdr::query()->where('uniqueid', $unique_id)->where('accountcode', 'Outbound')->where('disposition', 'ANSWERED')->first();
                            $cdr_data = Cdr::query()->where('uniqueid',$cdr_linked->linkedid )->where('accountcode', 'Outbound')->where('disposition', 'ANSWERED')->first();
                            $rating_data->agentId = $cdr_data->channel;
                            $rating_data->save();
                        }

		    $channel = \App\Models\Cdr::query()->where('uniqueid', $unique_id)->where('lastapp', 'Queue')->where('disposition', 'ANSWERED')->value('dstchannel');
		    $userId  = \App\Models\User::where('auth_username', '5655')->get()->value('id');
		    $callId  = \App\Models\QueueLog::where('callid', $unique_id)->where('data', '!=', '')->get()->value('data');

                    \Log::info("Unique__ID : {$unique_id} && Rating__Data : {$rating_data->rating} && Call__id : {$callId} && UserId : {$userId} ");
                    $this->endCall($userId, $callId, $rating_data->rating);


                } catch (\Exception $exception) {
                    dump($exception->getTrace()[0]);
                    $this->info("[{$date->toDateTimeString()}] INFO: Exception occurred: {$exception->getMessage()}");
                }
            } elseif ($arg === 'voicemail' && $this->checkTimings()) {
                $setting = VoicemailSetting::with('voicemailFile')->first();
                //$logger = $agiClient->getAsteriskLogger();
                //$logger->notice('Starting sync execution of call...');
                $fileName = "voicemail-$callerId-{$date->format('Ymd-His')}-$uniqueId.wav";
                $fileLoc = "{$date->format('Y')}/{$date->format('m')}/{$date->format('d')}/$fileName";
                $filePath = "/var/spool/asterisk/recording/$fileLoc";
                try {
                    $agiClient->answer();
                    $agiClient->streamFile($setting->voicemailFile->fileloc);
                    //$result = $agiClient->record("/var/spool/asterisk/recording/voicemail-200", "wav", '#');
                    $result = $agiClient->exec("Record", ["$filePath", '0', '0', 'k']);
                    if($result->getResult()) {
                        $voicemail = new \App\Models\Voicemail();
                        $voicemail->filePath = $filePath;
                        $voicemail->fileName = $fileName;
                        $voicemail->fileLoc = $fileLoc;
                        $voicemail->uniqueid = $uniqueId;
                        $voicemail->number = $callerId;
                        $voicemail->save();
                    }
                    $agiClient->hangup(true);
                } catch (ChannelDownException $channelDownException) {
                    $this->info("[{$date->toDateTimeString()}] ERROR: Can't hangup channel, probably user has already hung up: $callerId");
                } catch (\Exception $exception) {
                    $this->info("[{$date->toDateTimeString()}] ERROR: {$exception->getMessage()}");
                }
                $this->info("[{$date->toDateTimeString()}] INFO: Ending invoke script...");
            } else {
                // Continue in dialplan
                $agiClient->asyncBreak();
            }
        }, function ($event) use ($client) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }
}
