<?php

namespace App\Exports;

use App\Models\Cdr;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;


class CdrExport implements FromQuery, WithHeadings, WithMapping

{
    /**
    * @return \Illuminate\Support\Collection
    */

   public function queryRE(){

        //dd($input);
        $date = Carbon::now();
            $data = Cdr::query()
            ->select(['users.name','uniqueid', 'recordingfile', 'accountcode', 'src', 'dst', 'channel', 'dstchannel', 'disposition', 'duration', 'start', 'end','transcription'])
            ->leftJoin("users", function ($join){
                $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
            ->whereDate('start', '>=', $date->startOfDay())
            ->whereDate('end', '<=', $date->endOfDay())
            ->orderBy('start', 'desc');
       // dd($data);
        return $data;
   }

public function  query($requset){

 $data = Cdr::query()
            ->select([
                'users.name',
                'work_codes.name as workcode',
                'queue_log.queuename',
                'cdr.uniqueid',
                'cdr.recordingfile',
                'cdr.accountcode',
                'cdr.src',
                'cdr.dst',
                'cdr.channel',
                'cdr.dstchannel',
                'cdr.disposition',
                'cdr.duration',
                'cdr.start',
                'cdr.end',
                'cdr.transcription'
            ])
            ->leftJoin('users', function ($join) {
                $join->on(DB::raw("cdr.channel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                     ->orOn(DB:

    public function map($data): array{
        
        return[
            $data['name'],
            //$data['uniqueid'],
            $data['recordingfile'],
            $data['accountcode'],
            $data['src'] ,
            $data['dst'] ,
            $data['channel'],
            $data['dstchannel'],
            $data['disposition'],
            $data['duration'],
            $data['start'],
$data['transcription'],
            $data['end']
        ];
    }

    public function headings() :array{

        return[
            'Name',
           // 'Unique ID',
            'Recording File Name',
            'Call Type',
            'Source',
            'Destination',
            'Channel',
            'Dst Channel',
'Disposition',
            'Call Status',
            'Duration',
            'Start',
'Transcription',
            'End',
        ];
    }
}
