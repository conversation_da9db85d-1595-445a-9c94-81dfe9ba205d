<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\QueueRole;

class QueueRoleController extends Controller
{
    public function index(Request $request)
    {
        try {
            $validateData = $request->validate([
                'role' => 'required'
            ]);

            $data = QueueRole::where('role_id', $validateData['role'])->get()->toArray();

            return response()->json([
                'status' => 200,
                'data' => $data,
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 500,
                'message' => 'Server error',
                'error' => $th->getMessage(),
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            // $validateData = $request->validate([
            //     'role' => 'required',
            //     'queue' => 'required|array',
            // ]);

            // $roleId = $validateData['role'];
            // $newQueues = $validateData['queue'];

            $roleId = $request->role;
            $newQueues = $request->queue;

            $existingQueues = QueueRole::where('role_id', $roleId)->pluck('queue_name')->toArray();
            $toDelete = array_diff($existingQueues, $newQueues);
            if (!empty($toDelete)) {
                QueueRole::where('role_id', $roleId)->whereIn('queue_name', $toDelete)->delete();
            }

            foreach ($newQueues as $queueName) {
                QueueRole::updateOrCreate(
                    [
                        'role_id' => $roleId,
                        'queue_name' => $queueName,
                    ]
                );
            }

            return response()->json([
                'status' => 200,
                'message' => 'Queue roles updated/created successfully.',
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 500,
                'message' => 'Server error',
                'error' => $th->getMessage(),
            ], 500);
        }
    }
}
