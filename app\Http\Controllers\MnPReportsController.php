<?php

namespace App\Http\Controllers;

use App\Exports\ExportKPIReport;
use App\Models\Cdr;
use App\Models\Queue;
use App\Models\QueueLog;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\CarbonPeriod;
use Carbon\CarbonInterval;
use App\Models\MnpReports;
use Exception;
use App\Http\Controllers\TestController;

class MnPReportsController extends Controller
{
    public function callQueueSummaryReport_backup(Request $request)
    {
        try {
            $threshold = Queue::first('servicelevel');
            $differnce = 0;
            $totalAbd = 0;
            $queue = $request->queue ?? 300;
            $cdr = DB::table('queue_log')->where('queuename', $queue)->where('time', 'LIKE', "{$request->month}%")
                ->groupBy(DB::raw('Date(time)'))
                ->select(
                    DB::raw("
                        Date(time),
                        SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON') THEN 1 ELSE 0 END) as totalInbounCalls,
                        SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON') THEN 1 ELSE 0 END)*100),0) as CustomerServiceFactor,
                        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON') THEN 1 ELSE 0 END) *100),0) as percentageOfAnsweredCalls")
                )
                ->get();
            foreach ($cdr as $log) {
                $differnce = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
                $totalAbd = $log->totalAbandonCalls + $differnce;
                $log->totalAbandonCalls = $totalAbd;
            }


            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function callQueueSummaryReport(Request $request)
    {
        try {
            $threshold = Queue::first('servicelevel');
            $differnce = 0;
            $totalAbd = 0;

            $cdr = DB::table('queue_log');

            if ($request->filled('queue')) {
                $cdr->where('queuename', $request->queue);
            } else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
                $cdr->whereIn('queuename', $queues);
            }

            $cdr = $cdr->where('time', 'LIKE', "{$request->month}%")
                ->groupBy(DB::raw('Date(time)'))
                ->selectRaw("
                    Date(time),
                    SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= ?) THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON') THEN 1 ELSE 0 END)*100),0) as CustomerServiceFactor,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON') THEN 1 ELSE 0 END)*100),0) as percentageOfAnsweredCalls
                ", [$threshold->servicelevel])
                ->get();


            foreach ($cdr as $log) {
                $differnce = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
                $totalAbd = $log->totalAbandonCalls + $differnce;
                $log->totalAbandonCalls = $totalAbd;
            }


            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function serviceLevelReport(Request $request) //
    {
        $threshold = Queue::first('servicelevel');

        if (isset($request->queue) && !empty($request->queue)) {
            $queues = [$request->queue];
        } else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
        }

        if ($request->type == 'agent') {
            $reports = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->whereIn('Event', ['CONNECT', 'ABANDON', 'RINGNOANSWER'])
                ->where('Agent', '!=', 'None')
                ->whereIn('queuename', $queues)
                ->groupBy('agent')
                ->select(
                    DB::raw("
                    agent,
                    (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                        +SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))
                        /(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                        +SUM(CASE WHEN (Event = 'RINGNOANSWER') THEN 1 ELSE 0 END))*100 AS SLA
                    ")
                )
                ->get();
        } else {
            $reports = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->whereIn('Event', ['CONNECT', 'ABANDON'])
                ->whereIn('queuename', $queues)
                ->groupBy(DB::raw("DATE(time)"))
                ->select(
                    DB::raw("
            Date(time) as date,

            (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))
                /(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END))*100 AS SLA
            ")
                )
                ->get();
        }
        foreach ($reports as $report) {
            if ($report->SLA == NUll)
                $report->SLA = '0.00%';
            else
                $report->SLA = round($report->SLA, 2) . "%";
        }

        return response()->json($reports);
    }

    public function queueWiseReport_backup(Request $request)
    {
        $threshold = Queue::first('servicelevel');
        $differnce = 0;
        $totalAbd = 0;
        $queue_log = DB::table('queue_log')
            ->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
            ->where('queuename', '!=', 'NONE')
            ->groupBy('queuename')
            ->select(
                DB::raw("
                    queuename,
                    SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                    /SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT') THEN 1 ELSE 0 END)*100,2),0) as CustomerServiceFactor,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                    /SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT') THEN 1 ELSE 0 END) *100,2),0) as percentageOfAnsweredCalls")
            )->get();
        foreach ($queue_log as $log) {
            $differnce = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
            $totalAbd = $log->totalAbandonCalls + $differnce;
            $log->totalAbandonCalls = $totalAbd;
        }


        return response()->json($queue_log);
    }

    public function queueWiseReport(Request $request)
    {
        $threshold = Queue::first('servicelevel');
        $difference = 0;
        $totalAbd = 0;

        $queue_log = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to]);

        if ($request->filled('queue')) {
            $queue_log = $queue_log->where('queuename', $request->queue);
        } else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            $queue_log = $queue_log->whereIn('queuename', $queues)->where('queuename', '!=', 'NONE');
        }

        $queue_log = $queue_log
            ->groupBy('queuename')
            ->selectRaw("
                queuename,
                SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT') THEN 1 ELSE 0 END) as totalInbounCalls,
                SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= ?) THEN 1 ELSE 0 END)
                /SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT') THEN 1 ELSE 0 END)*100,2),0) as CustomerServiceFactor,
                IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                /SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT') THEN 1 ELSE 0 END) *100,2),0) as percentageOfAnsweredCalls
            ", [$threshold->servicelevel]);

        $queue_logs = $queue_log->get();

        foreach ($queue_logs as $log) {
            $difference = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
            $totalAbd = $log->totalAbandonCalls + $difference;
            $log->totalAbandonCalls = $totalAbd;
        }

        return response()->json($queue_logs);
    }


    // public function getAgentKPIReport(Request $request)
    // {

    //     $end = $request->end ?? now()->toDateString();
    //     $start = $request->start ??  now()->firstOfMonth()->toDateString();
    //     $period = CarbonPeriod::create($start , $end);
    //     $users = $this->getUsers($start, $end, $request->queue);
    //     $threshold = Queue::where('name', $request->queue)->first();
    //     $total = [];
    //     $totalData = [];

    //     $TotalLoginTime = 0; $TotalBreakTime = 0; $TotalHoldTime = 0; $TotalTalkTime = 0; $TotalIncomingCalls =0; $TotalAnsweredCalls=0; $TotalAbandonedCalls=0; $TotalThresholdCalls=0; $TotalOutboundCalls=0; $TotalAHT=0; $TotalAgentProductivity=0; $TotalAHTOutput=0;

    //         foreach($users as $user){
    //             $loginTime = 0; $breakTime = 0; $holdTime = 0; $talkTime = 0; $incomingCalls =0; $answeredCalls=0;$abandonedCalls=0; $thresholdCalls=0; $outboundCalls=0; $AHT=0; $agentProductivity=0; $AHTOutput=0;

    //             foreach($period as $dt){
    //                 //for logged-in time calculation
    //                 $data = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('ADDMEMBER', 'REMOVEMEMBER') AND Agent = '$user->name' AND DATE(time) = '$dt' ORDER BY TIME ASC");
    //                 for($i=0; $i < count($data); $i++ ){
    //                     if($data[$i]->Event === 'ADDMEMBER') {
    //                         // Search for remove member
    //                         if($i !== count($data)-1 && $data[$i + 1]->Event === 'REMOVEMEMBER') {
    //                             // Take difference
    //                             $loginTime += Carbon::parse($data[$i + 1]->time)->diffInSeconds($data[$i]->time);
    //                         }else if($dt->format('Y-m-d') === Carbon::now()->format('Y-m-d')) {
    //                             $removeMember = Carbon::now()->format('Y-m-d H:i:s');
    //                             $loginTime += Carbon::parse($removeMember)->diffInSeconds($data[$i]->time);
    //                         }else{
    //                             $removeMember = $dt->copy()->endOfDay()->format('Y-m-d H:i:s');
    //                             $loginTime += Carbon::parse($removeMember)->diffInSeconds($data[$i]->time);
    //                         }
    //                     }
    //                 }
    //                 //for users total break
    //                 $breakData = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('PAUSE', 'UNPAUSE') AND Agent = '$user->name' AND DATE(time) = '$dt' ORDER BY TIME ASC");
    //                 for($x=0; $x < count($breakData); $x++ ){
    //                     if($breakData[$x]->Event === 'PAUSE') {
    //                         // Search for UNPAUSE
    //                         if($x !== count($breakData)-1 && $breakData[$x + 1]->Event === 'UNPAUSE') {
    //                             // Take difference
    //                             $breakTime += Carbon::parse($breakData[$x + 1]->time)->diffInSeconds($breakData[$x]->time);
    //                         }else if($dt->format('Y-m-d') === Carbon::now()->format('Y-m-d')) {
    //                             $unpause = Carbon::now()->format('Y-m-d H:i:s');
    //                             $breakTime += Carbon::parse($unpause)->diffInSeconds($breakData[$x]->time);
    //                         }else{
    //                             $unpause = $dt->copy()->endOfDay()->format('Y-m-d H:i:s');
    //                             $breakTime += Carbon::parse($unpause)->diffInSeconds($breakData[$x]->time);
    //                         }
    //                     }
    //                 }

    //                 //for hold time
    //                 $holdData = DB::select("SELECT * FROM queue_log WHERE EVENT IN('HOLD','UNHOLD','COMPLETECALLER' ,'COMPLETEAGENT', 'BLINDTRANSFER') AND Agent = '$user->name' AND callid != 'NONE' AND DATE(time) = '$dt' ORDER BY TIME ASC");
    //                 for($y=0; $y < count($holdData); $y++ ){
    //                     if($holdData[$y]->Event === 'HOLD')
    //                     {
    //                         if($y !== count($holdData)-1 && ($holdData[$y + 1]->Event === 'UNHOLD' || $holdData[$y + 1]->Event === 'COMPLETECALLER' || $holdData[$y + 1]->Event === 'COMPLETEAGENT' || $holdData[$y + 1]->Event === 'BLINDTRANSFER') && $holdData[$y]->callid === $holdData[$y + 1]->callid) {
    //                             // Take difference
    //                             $holdTime += Carbon::parse($holdData[$y + 1]->time)->diffInSeconds($holdData[$y]->time);
    //                         }
    //                     }
    //                     // if($holdData[$y]->callid !== 'NONE')
    //                     // {
    //                     //     $callid =  $holdData[$y]->callid;
    //                     //     $unhold = DB::select("SELECT * FROM queue_log WHERE EVENT = 'UNHOLD' AND callid = '$callid'");
    //                     //     //if unhold event found
    //                     //     if($unhold){
    //                     //         $holdTime += Carbon::parse($unhold[0]->time)->diffInSeconds($holdData[$y]->time);
    //                     //     }else if($unhold = DB::select("SELECT * FROM queue_log WHERE EVENT IN( 'COMPLETECALLER' ,'COMPLETEAGENT', 'BLINDTRANSFER') AND callid = '$callid'")){ // if call disconnected without unhold event
    //                     //         //$holdTime += Carbon::parse($unhold[0]->time)->diffInSeconds($holdData[$y]->time);
    //                     //         if($unhold[0]->time < $holdData[$y]->time)
    //                     //         {
    //                     //             $holdTime += Carbon::parse($holdData[$y]->time)->diffInSeconds($holdData[$y]->time);
    //                     //         }
    //                     //         else
    //                     //         {
    //                     //             $holdTime += Carbon::parse($unhold[0]->time)->diffInSeconds($holdData[$y]->time);
    //                     //         }

    //                     //     }else{
    //                     //         $holdTime += Carbon::parse($holdData[$y]->time)->diffInSeconds($holdData[$y]->time);
    //                     //     }
    //                     // }
    //                 }

    //                 $thresholdValue = $threshold->servicelevel;
    //                 $query = "
    //                 SELECT
    //                     COUNT(CASE WHEN Event IN ('CONNECT', 'RINGNOANSWER') THEN 1 END) as incomingCalls,
    //                     COUNT(CASE WHEN Event = 'CONNECT' THEN 1 END) as answeredCalls,
    //                     COUNT(CASE WHEN Event = 'RINGNOANSWER' THEN 1 END) as abandonedCalls,
    //                     COUNT(CASE WHEN Event = 'CONNECT' AND data1 <= :threshold THEN 1 END) as thresholdCalls,
    //                     SUM(CASE WHEN Event IN ('COMPLETECALLER', 'COMPLETEAGENT') THEN data2 ELSE 0 END) as talkTime

    //                 FROM
    //                     queue_log
    //                 WHERE
    //                     Agent = :user
    //                 AND
    //                     DATE(time) = :date
    //                 ";

    //                 $data = DB::select($query, ['user' => $user->name, 'date' => $dt, 'threshold' => $thresholdValue]);
    //                 $incomingCalls += $data[0]->incomingCalls;
    //                 $answeredCalls += $data[0]->answeredCalls;
    //                 $abandonedCalls += $data[0]->abandonedCalls;
    //                 $thresholdCalls += $data[0]->thresholdCalls;
    //                 $talkTime += $data[0]->talkTime;
    //                 //for outbound call
    //                 if(isset($user->auth_username)) {
    //                     $outboundCalls += Cdr::where('accountcode', 'Outbound')->where('channel' , 'LIKE', DB::raw("CONCAT('%PJSIP/', $user->auth_username, '%')"))->whereDate('start' ,$dt)->count();
    //                 }
    //             }

    //             if($answeredCalls != 0){
    //                 $AHT +=  ($talkTime + $holdTime + $threshold->wrapuptime) / $answeredCalls;
    //                 $AHTSeconds = round($AHT);
    //                 $AHTOutput = sprintf('%02d:%02d:%02d', ($AHTSeconds/ 3600),($AHTSeconds/ 60 % 60), $AHTSeconds% 60);
    //             }
    //             //for agent productivity
    //             if($loginTime !=0){
    //                 $agentProductivity += round(($talkTime / $loginTime ) * 100);
    //             }


    //             $loginSeconds = round($loginTime);
    //             $loginOutput = sprintf('%02d:%02d:%02d', ($loginSeconds/ 3600),($loginSeconds/ 60 % 60), $loginSeconds% 60);

    //             $breakSeconds = round($breakTime);
    //             $breakOutput = sprintf('%02d:%02d:%02d', ($breakSeconds/ 3600),($breakSeconds/ 60 % 60), $breakSeconds% 60);

    //             $talkSeconds = round($talkTime);
    //             $talkOutput = sprintf('%02d:%02d:%02d', ($talkSeconds/ 3600),($talkSeconds/ 60 % 60), $talkSeconds% 60);

    //             $holdSeconds = round($holdTime);
    //             $holdOutput = sprintf('%02d:%02d:%02d', ($holdSeconds/ 3600),($holdSeconds/ 60 % 60), $holdSeconds% 60);

    //             $total[] = [
    //                 'Agent' => $user->name,
    //                 'loginTime' => $loginOutput,
    //                 'breakTime' => $breakOutput,
    //                 'incomingCalls' => $incomingCalls,
    //                 'answeredCalls' => $answeredCalls,
    //                 'abandonedCalls' => $abandonedCalls,
    //                 'thresholdCalls' => $thresholdCalls,
    //                 'outboundCalls' => $outboundCalls,
    //                 'talkTime' => $talkOutput,
    //                 'holdTime' => $holdOutput,
    //                 'AHT' => ($AHTOutput == 0) ? '00:00:00' : $AHTOutput,
    //                 'agentProductivity' => $agentProductivity.'%'
    //             ];

    //             //assigning total values of a user in grand total variables
    //             $TotalLoginTime += $loginTime;
    //             $TotalBreakTime += $breakTime;
    //             $TotalIncomingCalls += $incomingCalls;
    //             $TotalAnsweredCalls += $answeredCalls;
    //             $TotalAbandonedCalls += $abandonedCalls;
    //             $TotalThresholdCalls += $thresholdCalls;
    //             $TotalOutboundCalls += $outboundCalls;
    //             $TotalTalkTime += $talkTime;
    //             $TotalHoldTime += $holdTime;
    //         }

    //         //calculation for grandtotal

    //         if($TotalAnsweredCalls != 0){
    //             $TotalAHT +=  ($TotalTalkTime + $TotalHoldTime + $threshold->wrapuptime) / $TotalAnsweredCalls;
    //             $TotalAHTSeconds = round($TotalAHT);
    //             $TotalAHTOutput = sprintf('%02d:%02d:%02d', ($TotalAHTSeconds/ 3600),($TotalAHTSeconds/ 60 % 60), $TotalAHTSeconds% 60);
    //         }
    //         //for agent productivity
    //         if($TotalLoginTime !=0){
    //             $TotalAgentProductivity += round(($TotalTalkTime / $TotalLoginTime ) * 100);
    //         }

    //         $TotalLoginSeconds = round($TotalLoginTime);
    //         $TotalLoginOutput = sprintf('%02d:%02d:%02d', ($TotalLoginSeconds/ 3600),($TotalLoginSeconds/ 60 % 60), $TotalLoginSeconds% 60);

    //         $TotalBreakSeconds = round($TotalBreakTime);
    //         $TotalBreakOutput = sprintf('%02d:%02d:%02d', ($TotalBreakSeconds/ 3600),($TotalBreakSeconds/ 60 % 60), $TotalBreakSeconds% 60);

    //         $TotalTalkSeconds = round($TotalTalkTime);
    //         $TotalTalkOutput = sprintf('%02d:%02d:%02d', ($TotalTalkSeconds/ 3600),($TotalTalkSeconds/ 60 % 60), $TotalTalkSeconds% 60);

    //         $TotalHoldSeconds = round($TotalHoldTime);
    //         $TotalHoldOutput = sprintf('%02d:%02d:%02d', ($TotalHoldSeconds/ 3600),($TotalHoldSeconds/ 60 % 60), $TotalHoldSeconds% 60);

    //         $totalData[] =[
    //             'Agent' => 'Total',
    //             'loginTime' => $TotalLoginOutput,
    //             'breakTime' => $TotalBreakOutput,
    //             'incomingCalls' => $TotalIncomingCalls,
    //             'answeredCalls' => $TotalAnsweredCalls,
    //             'abandonedCalls' => $TotalAbandonedCalls,
    //             'thresholdCalls' => $TotalThresholdCalls,
    //             'outboundCalls' => $TotalOutboundCalls,
    //             'talkTime' => $TotalTalkOutput,
    //             'holdTime' => $TotalHoldOutput,
    //             'AHT' => ($TotalAHTOutput == 0) ?  '00:00:00' : $TotalAHTOutput,
    //             'agentProductivity' => $TotalAgentProductivity.'%',
    //         ];
    //         // //dd($data);
    //         $grandTotal = array_merge($total , $totalData);
    //         return response()->json($grandTotal);
    // }

    public function getAgentKPIReport(Request $request)
    {
        $data = [];

        if ($request->type == 'date') {
            $data = MnpReports::where('report_date', $request->start)->where('report_name', 'Agent Wise Report')->where('report_type', 'Daily');

            if (isset($request->queue) && !empty($request->queue)) {
                $queues = [$request->queue];
            } else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }

            $data = $data->whereIn('queue', $queues)->pluck('data');
        } elseif ($request->type == 'month') {
            $data = MnpReports::where('report_month', $request->month)->where('report_name', 'Agent Wise Report')->where('report_type', 'Monthly');

            if (isset($request->queue) && !empty($request->queue)) {
                $queues = [$request->queue];
            } else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }

            $data = $data->whereIn('queue', $queues)->pluck('data');
        }
        return response()->json($data);
    }

    public function getAgentKPIReportModified(Request $request)
    {
        $date = Carbon::parse($request->start)->subDay()->toDateString();
        $data = MnpReports::where('report_date', $date)
            ->where('report_name', 'Agent Wise Report')
            ->where('report_type', 'Daily');
        $results = $data->pluck('data')->collapse();
        $apiKey = (new TestController())->getApiKey();
        $report_date = MnpReports::value('report_date');
        $reports = [];
        foreach ($results as $result) {
            $agentName = $result['Agent'] ?? null;
            $agentId = User::where('name', $agentName)->value('id');
            $reports[] = [
                "agentid" => $agentId ?? null,
                "agentname" => $result['Agent'] ?? null,
                "dayenddate" => $report_date ?? null,
                "loggedintime" => $report_date . " " . $result['loginTime'] ?? null,
                "breaktime" => $report_date . " " . $result['breakTime'] ?? null,
                "incomingcalls" => $result['incomingCalls'] ?? null,
                "answeredincomingcalls" => $result['answeredCalls'] ?? null,
                "abandonedincomingcalls" => $result['abandonedCalls'] ?? null,
                "answeredwithinthreshold" => $result['thresholdCalls'] ?? null,
                "outgoingcalls" => $result['outboundCalls'] ?? null,
                "talktimeincoming" => $report_date . " " . $result['talkTime'] ?? null,
                "totalholdtime" => $report_date . " " . $result['holdTime'] ?? null,
                "averagecallhandlingtime" => $report_date . " " . $result['AHT'] ?? null,
                "agentproductivity" => rtrim($result['agentProductivity'], '%') ?? null,
                "ApiKey" => $apiKey ?? null
            ];
        }
        return response()->json($reports);
    }

    public function getCLIAbandonCalls_backup(Request $request)
    {
        $array = [];


        $callIDs = DB::table('queue_log')->where('data2', $request->contactNumber)->get('callid');
        foreach ($callIDs as $callID) {
            $cli = DB::table('queue_log')->where('callid', $callID->callid)->where('Event', 'ABANDON');
            if ($request->has('from') && $request->has('to')) {

                $cli->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to]);
            }


            foreach ($cli->orderBy(DB::raw('DATE(time)'), 'DESC')->get() as $arr) {
                $date = strtotime($arr->time);

                $array[] = [
                    'Date' => date('Y-m-d', $date),
                    'callid' => $arr->callid = $request->contactNumber,
                    'timeEnd' => $arr->timeEnd = Carbon::create($arr->time)->format('g:i A'),
                    'timeStart' => $arr->timeStart = Carbon::create($arr->time)->subSecond($arr->data3)->format('g:i A'),
                    'data3' => $arr->data3
                ];
            }
        }
        $sort = arsort($array);

        return response()->json(array_values($array));
    }

    public function getCLIAbandonCalls(Request $request)
    {
        ini_set('memory_limit', -1);
        set_time_limit(0);

        $request->validate([
            'contactNumber' => 'required'
        ]);

        $array = [];

        $callIDs = DB::table('queue_log')->where('data2', $request->contactNumber)->get('callid');
        foreach ($callIDs as $callID) {
            $cli = DB::table('queue_log')->where('callid', $callID->callid)->whereIn('Event', ['ABANDON', 'EXITWITHKEY']);
            if ($request->has('from') && $request->has('to')) {
                $cli->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to]);
            }

            if (isset($request->queue) && !empty($request->queue)) {
                $queues = [$request->queue];
            } else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }

            $cli->whereIn('queuename', $queues);

            foreach ($cli->orderBy(DB::raw('DATE(time)'), 'DESC')->get() as $arr) {
                $date = strtotime($arr->time);

                $array[] = [
                    'Date' => date('Y-m-d', $date),
                    'callid' => $arr->callid = $request->contactNumber,
                    'timeEnd' => $arr->timeEnd = Carbon::create($arr->time)->format('g:i A'),
                    'timeStart' => $arr->timeStart = Carbon::create($arr->time)->subSecond($arr->data3)->format('g:i A'),
                    'data3' => $arr->data3
                ];
            }
        }
        $sort = arsort($array);

        return response()->json(array_values($array));
    }

    public function workCodeWiseDailyReport(Request $request)
    {
        $newArray = array();
        $array = array();
        try {
            $cdrs = Cdr::query()->where('lastapp', 'Queue')
                ->join('queue_log', 'uniqueid', '=', 'queue_log.callid')
                ->where('Event', 'WORKCODE')->join('work_codes', 'id', '=', 'queue_log.data1')
                ->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->select(DB::raw('DATE(time) AS date'), 'name as call_status', DB::raw('count(*) as count'))
                ->groupBy(DB::raw('DATE(time)', 'name'))
                ->orderBy(DB::raw('DATE(time)'))->get();

            foreach ($cdrs as $key => $cdr) {


                $newArray[$key][$cdr->date] = $cdr->count;
                // $array[] =['workcode'=> $cdr->call_status,$newArray ];
            }


            return response()->json($newArray);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function agentStatus_Working(Request $request)
    {
        $request->validate([
            'date' => 'required',
            'from' => 'required',
            'to' => 'required',
        ]);

        $users = $request->agents;
        $date = $request->date;
        $from = $request->from;
        $to = $request->to;

        $data = [];
        $holdData = [];

        $queryBuilder = QueueLog::query();

        if ($users) {
            $queryBuilder->whereIn('Agent', $users);
        }
        if ($date) {
            $queryBuilder->whereDate('time', $date);
        }
        if ($from) {
            $queryBuilder->whereTime('time', '>=', $from);
        }
        if ($to) {
            $queryBuilder->whereTime('time', '<=', $to);
        }

        $records = $queryBuilder->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE'])->get();
        $hold = $queryBuilder->whereIn('Event', ['HOLD', 'UNHOLD', 'COMPLETECALLER', 'COMPLETEAGENT', 'PAUSE'])->get();

        // Process hold data separately
        for ($y = 0; $y < count($hold); $y++) {
            $time = Carbon::parse($hold[$y]->time)->format('H:i');

            if ($hold[$y]->Event == 'HOLD' && $y !== count($hold) - 1 && ($hold[$y + 1]->Event == 'UNHOLD' || $hold[$y + 1]->Event == 'REMOVEMEMBER' || $hold[$y + 1]->Event == 'COMPLETEAGENT' || $hold[$y + 1]->Event == 'COMPLETECALLER')) {
                $timeOut = Carbon::parse($hold[$y + 1]->time)->format('H:i');
                $holdData[] = [
                    'agentName' => $hold[$y]->Agent,
                    'Time-in' => $time,
                    'Time-out' => $timeOut,
                    'Status' => 'On-Hold',
                    'timeForSorting' => $time
                ];
            }
        }

        // Process records
        for ($x = 0; $x < count($records); $x++) {
            if ($records[$x]->Event == 'ADDMEMBER') {
                $time = Carbon::parse($records[$x]->time)->format('H:i');
                $data[] = [
                    'agentName' => $records[$x]->Agent,
                    'Time-in' => $time,
                    'Time-out' => '-',
                    'Status' => 'Logged In',
                    'timeForSorting' => $time
                ];
                if ($x !== count($records) - 1 && ($records[$x + 1]->Event == 'PAUSE' || $records[$x + 1]->Event == 'REMOVEMEMBER')) {
                    $timeOut = Carbon::parse($records[$x + 1]->time)->format('H:i');
                    $data[] = [
                        'agentName' => $records[$x]->Agent,
                        'Time-in' => $time,
                        'Time-out' => $timeOut,
                        'Status' => 'Ready',
                        'timeForSorting' => $time
                    ];
                } else {
                    $data[] = [
                        'agentName' => $records[$x]->Agent,
                        'Time-in' => $time,
                        'Time-out' => '-',
                        'Status' => 'Ready',
                        'timeForSorting' => $time
                    ];
                }
            } elseif ($records[$x]->Event == 'REMOVEMEMBER') {
                $time = Carbon::parse($records[$x]->time)->format('H:i');
                $data[] = [
                    'agentName' => $records[$x]->Agent,
                    'Time-in' => '-',
                    'Time-out' => $time,
                    'Status' => 'Logged Out',
                    'timeForSorting' => $time
                ];
            } elseif ($records[$x]->Event == 'PAUSE') {
                $timeOut = $x !== count($records) - 1 && ($records[$x + 1]->Event == 'UNPAUSE' || $records[$x + 1]->Event == 'REMOVEMEMBER') ? Carbon::parse($records[$x + 1]->time)->format('H:i') : '-';
                $time = Carbon::parse($records[$x]->time)->format('H:i');

                $data[] = [
                    'agentName' => $records[$x]->Agent,
                    'Time-in' => $time,
                    'Time-out' => $timeOut,
                    'Status' => "Break" . "-" . ($records[$x]->data1),
                    'timeForSorting' => $time
                ];
            } elseif ($records[$x]->Event == 'UNPAUSE') {
                $time = Carbon::parse($records[$x]->time)->format('H:i');

                if ($x !== count($records) - 1 && ($records[$x + 1]->Event == 'PAUSE' || $records[$x + 1]->Event == 'REMOVEMEMBER')) {
                    $timeOut = Carbon::parse($records[$x + 1]->time)->format('H:i');
                    $data[] = [
                        'agentName' => $records[$x]->Agent,
                        'Time-in' => $time,
                        'Time-out' => $timeOut,
                        'Status' => 'Ready',
                        'timeForSorting' => $time
                    ];
                } else {
                    $data[] = [
                        'agentName' => $records[$x]->Agent,
                        'Time-in' => $time,
                        'Time-out' => '-',
                        'Status' => 'Ready',
                        'timeForSorting' => $time
                    ];
                }
            }
        }

        $result = array_merge($data, $holdData);
        array_multisort(array_column($result, "timeForSorting"), SORT_ASC, $result);
        return response()->json($result);
    }

    public function agentStatus(Request $request)
    {
        $request->validate([
            'date' => 'required',
            'from' => 'required',
            'to' => 'required',
        ]);

        $users = $request->agents;
        $date = $request->date;
        $from = $request->from;
        $to = $request->to;

        $data = [];
        $holdData = [];

        $queryBuilder = QueueLog::query();

        if ($users) {
            $queryBuilder->whereIn('Agent', $users);
        }
        if ($date) {
            $queryBuilder->whereDate('time', $date);
        }
        if ($from) {
            $queryBuilder->whereTime('time', '>=', $from);
        }
        if ($to) {
            $queryBuilder->whereTime('time', '<=', $to);
        }

        if (isset($request->queue) && !empty($request->queue)) {
            $queues = [$request->queue];
            $queryBuilder->whereIn('queue_log.queuename', $queues);
        } /*else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
        }
        $queryBuilder->whereIn('queue_log.queuename', $queues);*/

        $records = $queryBuilder->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE'])->get();
        $hold = $queryBuilder->whereIn('Event', ['HOLD', 'UNHOLD', 'COMPLETECALLER', 'COMPLETEAGENT', 'PAUSE'])->get();
        // $breakRecords = $queryBuilder->whereIn('Event', ['PAUSE', 'UNPAUSE'])->whereNotIn('data1', ['Bio Break', 'Working on CXM', 'Namaz with lunch break', 'Counselling/Training', 'Support on HFC Desk'])->get();

        // Process hold data separately
        for ($y = 0; $y < count($hold); $y++) {
            $time = Carbon::parse($hold[$y]->time)->format('H:i:s');

            if ($hold[$y]->Event == 'HOLD' && $y !== count($hold) - 1 && ($hold[$y + 1]->Event == 'UNHOLD' || $hold[$y + 1]->Event == 'REMOVEMEMBER' || $hold[$y + 1]->Event == 'COMPLETEAGENT' || $hold[$y + 1]->Event == 'COMPLETECALLER')) {
                $timeOut = Carbon::parse($hold[$y + 1]->time)->format('H:i:s');
                $timeIn = Carbon::parse($hold[$y]->time);
                $timeDiff = $timeIn->diff($timeOut);

                $holdData[] = [
                    'agentName' => $hold[$y]->Agent,
                    'Time-in' => $time,
                    'Time-out' => $timeOut,
                    'Time-Difference' => $timeDiff->format('%H:%I:%S'),
                    'Status' => 'On-Hold',
                    'timeForSorting' => $time
                ];
            }
        }

        // Process records
        $timeInMap = [];
        for ($x = 0; $x < count($records); $x++) {
            $agentName = $records[$x]->Agent;
            $eventTime = Carbon::parse($records[$x]->time)->format('H:i:s');

            if ($records[$x]->Event == 'ADDMEMBER') {
                $timeInMap[$agentName] = Carbon::parse($records[$x]->time);

                $data[] = [
                    'agentName' => $agentName,
                    'Time-in' => $eventTime,
                    'Time-out' => '-',
                    'Time-Difference' => '-',
                    'Status' => 'Logged In',
                    'timeForSorting' => $eventTime
                ];
            } elseif ($records[$x]->Event == 'REMOVEMEMBER' && isset($timeInMap[$agentName])) {
                $timeOut = Carbon::parse($records[$x]->time);
                $timeIn = $timeInMap[$agentName];

                $duration = $timeOut->diffInSeconds($timeIn);
                $durations = sprintf('%02d:%02d:%02d', floor($duration / 3600), floor($duration / 60 % 60), $duration % 60);

                $data[] = [
                    'agentName' => $agentName,
                    'Time-in' => '-',
                    'Time-out' => $eventTime,
                    'Time-Difference' => $durations,
                    'Status' => 'Logged Out',
                    'timeForSorting' => $eventTime
                ];

                // Clear the login time for this agent after logging out
                unset($timeInMap[$agentName]);
            }
        }

        // Handle case where there's a login without a corresponding logout
        foreach ($timeInMap as $agentName => $loginTime) {
            // Set the logout time as the end of the day
            $logoutTime = Carbon::now()->endOfDay();

            // Calculate the duration between login and end of day
            $duration = $logoutTime->diffInSeconds($loginTime);
            $durations = sprintf('%02d:%02d:%02d', floor($duration / 3600), floor($duration / 60 % 60), $duration % 60);

            $data[] = [
                'agentName' => $agentName,
                'Time-in' => Carbon::parse($loginTime)->format('H:i:s'),
                'Time-out' => $logoutTime->format('H:i:s'),
                'Time-Difference' => $durations,
                'Status' => 'Logged Out',
                'timeForSorting' => Carbon::parse($loginTime)->format('H:i:s')
            ];
        }

        $pauseStartTimes = [];
        $breakData = [];

        foreach ($records as $logEntry) {
            $breakType = $logEntry->data1;
            $agentName = $logEntry->Agent;
            $eventTime = Carbon::parse($logEntry->time);
            $breakDurations = '';
            $timeOut = null;

            // Check if this is a PAUSE event
            if ($logEntry->Event == 'PAUSE') {
                // Store the pause start time for the break type and agent
                $pauseStartTimes[$agentName][$breakType] = $eventTime;
            }
            // Check if this is an UNPAUSE event and the corresponding PAUSE event exists
            elseif ($logEntry->Event == 'UNPAUSE' && isset($pauseStartTimes[$agentName][$breakType])) {
                // Calculate the time difference when unpausing
                $timeOut = Carbon::parse($logEntry->time);
                $pauseStartTime = $pauseStartTimes[$agentName][$breakType];
                $duration = $timeOut->diffInSeconds($pauseStartTime);

                // Format the duration as H:i:s
                $breakSeconds = round($duration);
                $breakDurations = sprintf('%02d:%02d:%02d', floor($breakSeconds / 3600), floor($breakSeconds / 60 % 60), $breakSeconds % 60);

                // Add break data entry (combining PAUSE and UNPAUSE events)
                $breakData[] = [
                    'agentName' => $agentName,
                    'Time-in' => $pauseStartTime->format('H:i:s'),
                    'Time-out' => $timeOut->format('H:i:s'),
                    'Time-Difference' => $breakDurations,
                    'Status' => $breakType,
                    'timeForSorting' => $pauseStartTime->format('H:i:s')
                ];

                // Reset the pause start time after processing
                unset($pauseStartTimes[$agentName][$breakType]);
            }
        }

        $result = array_merge($data, $holdData);
        $result = array_merge($result, $breakData);
        array_multisort(array_column($result, "timeForSorting"), SORT_ASC, $result);
        return response()->json($result);
    }

    private function getUsers($start, $end, $queue, $fetchFromQueueLog = true)
    {
        if ($fetchFromQueueLog) {
            return DB::select("select DISTINCT Agent as name, u.auth_username from queue_log left join users u on u.name = Agent where DATE(time) >= '$start' and DATE(time) <= '$end' and queuename = '$queue' and Agent <> 'NONE' group by Agent order by time desc");
        } else {
            return User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name', 'auth_username'])->whereIn('type', ['Blended', 'Inbound'])->where('queue_user.queue_name', $queue)->get();
        }
    }

    public function getAgents(Request $request)
    {

        $users = User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name', 'auth_username'])->whereIn('type', ['Blended', 'Inbound'])->where('queue_user.queue_name', $request->queue)->get();
        return response()->json($users);
    }

    public function hourlyAbandonReportBKP(Request $request)
    {
        set_time_limit(1200);

        $request->validate([
            'date' => 'required',
            'queue' => 'required|exists:queue_log,queuename'
        ]);

        try {
            $totalData = [];
            $date = $request->date;
            $timeFrom = '08:00:00';

            if ($date == Carbon::now()->format('Y-m-d')) {
                $timeTo = Carbon::now()->format('H:i:s.u');
                $intervals = CarbonInterval::minutes('60')->toPeriod($timeFrom, $timeTo)->toArray();
            } else {
                $timeTo = Carbon::parse($date)->endOfDay()->format('H:i:s.u');
                $intervals = array_merge(CarbonInterval::minutes('60')->toPeriod($timeFrom, $timeTo)->toArray(), [Carbon::parse("$date 23:59:59.999999")]);
            }

            $lastAgentCount = 0;
            $breakDurations = [
                'Bio Break',
                'Working on CXM',
                'Namaz with lunch break',
                'Counselling/Training',
                'Support on HFC Desk',
                'Not Submit Workcode Yet'
            ];

            if (count($intervals) > 1) {
                for ($i = 0; $i < count($intervals); $i++) {
                    $breakTimeDuration = 0;
                    $agent = 0;
                    $inboundCalls = 0;
                    $abandonCalls = 0;

                    if ($i !== count($intervals) - 1) {
                        $to = $intervals[$i + 1]->format('H:i');

                        $inboundCalls = QueueLog::whereIn('Event', ['ABANDON', 'CONNECT'])
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->count();

                        $abandonCalls = QueueLog::where('Event', 'ABANDON')
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->count();

                        $periodData = QueueLog::whereIn('Event', ['PAUSE', 'UNPAUSE'])
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->get();

                        $currentAgentCount = QueueLog::whereIn('Event', ['ADDMEMBER'])
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->distinct('agent')
                            ->count('agent');

                        $agent = $currentAgentCount == 0 ? $lastAgentCount : $currentAgentCount;

                        // foreach ($periodData as $pd) {
                        //     if( $pd->Event == 'PAUSE' && in_array($pd->data1, $breakDurations) ) {
                        //         $breakAgent++;
                        //     }
                        // }

                        $pauseStartTimes = [];
                        foreach ($periodData as $pd) {
                            $breakType = $pd->data1;

                            if (in_array($breakType, $breakDurations)) {
                                $eventTime = \Carbon\Carbon::parse($pd->time);

                                if ($pd->Event == 'PAUSE') {
                                    // Store the pause start time for the break type
                                    $pauseStartTimes[$breakType] = $eventTime;
                                } elseif ($pd->Event == 'UNPAUSE' && isset($pauseStartTimes[$breakType])) {
                                    // Calculate the time difference when unpausing
                                    $pauseStartTime = $pauseStartTimes[$breakType];
                                    $duration = $eventTime->diffInSeconds($pauseStartTime);

                                    // Add the duration to the appropriate break type
                                    $breakTimeDuration += $duration;

                                    // Reset the pause start time
                                    unset($pauseStartTimes[$breakType]);
                                }
                            }
                        }

                        // Convert minutes to hours
                        $totalBreakMints = $breakTimeDuration / 60;
                        $roundedBreakMints = round($totalBreakMints);
                        $totalBreakHours = $roundedBreakMints / 60;
                        $roundedBreakHours = ceil($totalBreakHours);

                        if ($inboundCalls !== 0) {
                            $acr = round(($abandonCalls / $inboundCalls) * 100);
                        } else {
                            $acr = 0;
                        }

                        $totalData[] = [
                            'from' => $intervals[$i]->format('H:i'),
                            'to' => $to ?? NULL,
                            'totalInboundCalls' => $inboundCalls,
                            'totalAbandonCalls' => $abandonCalls,
                            'agentBreak' => $roundedBreakHours,
                            'agentLogin' => $agent,
                            'acr' => ($acr > 100) ? '100%' : $acr . '%',
                        ];

                        // Update lastAgentCount for the next iteration
                        $lastAgentCount = $currentAgentCount;

                        // Check if the last agent count should be used
                        $lastAgentData = QueueLog::whereDate('time', $date)
                            ->where('queuename', $request->queue)
                            ->where('Event', 'ADDMEMBER')
                            ->whereNotExists(function ($query) use ($date, $request) {
                                $query->select(DB::raw(1))
                                    ->from('queue_log')
                                    ->whereDate('time', $date)
                                    ->where('queuename', $request->queue)
                                    ->where('Event', 'REMOVEMEMBER');
                            })
                            ->latest('time')
                            ->first();

                        if ($lastAgentData) {
                            $lastAgentCount = QueueLog::whereDate('time', $date)
                                ->where('queuename', $request->queue)
                                ->where('Event', 'ADDMEMBER')
                                ->count('agent');
                        }
                    }
                }
            }

            return response()->json($totalData, 200);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function hourlyAbandonReport(Request $request)
    {
        set_time_limit(1200);

        $request->validate([
            'date' => 'required',
            'queue' => 'required|exists:queue_log,queuename'
        ]);

        try {
            $totalData = [];
            $date = $request->date;
            $timeFrom = '08:00:00';

            if ($date == Carbon::now()->format('Y-m-d')) {
                $timeTo = Carbon::now()->format('H:i:s.u');
                $intervals = CarbonInterval::minutes('60')->toPeriod($timeFrom, $timeTo)->toArray();
            } else {
                $timeTo = Carbon::parse($date)->endOfDay()->format('H:i:s.u');
                $intervals = array_merge(CarbonInterval::minutes('60')->toPeriod($timeFrom, $timeTo)->toArray(), [Carbon::parse("$date 23:59:59.999999")]);
            }

            $lastAgentCount = 0;
            $breakDurations = [
                'Bio Break',
                'Working on CXM',
                'Namaz with lunch break',
                'Counselling/Training',
                'Support on HFC Desk',
                //'Not Submit Workcode Yet'
            ];

            // Carryover storage for PAUSE events between intervals
            $carryoverPauseEvents = [];

            if (count($intervals) > 1) {
                for ($i = 0; $i < count($intervals); $i++) {
                    $breakTimeDuration = 0;
                    $agent = 0;
                    $inboundCalls = 0;
                    $abandonCalls = 0;

                    if ($i !== count($intervals) - 1) {
                        $to = $intervals[$i + 1]->format('H:i');

                        $inboundCalls = QueueLog::whereIn('Event', ['ABANDON', 'CONNECT'])
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->count();

                        $abandonCalls = QueueLog::where('Event', 'ABANDON')
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->count();

                        $periodData = QueueLog::whereIn('Event', ['PAUSE', 'UNPAUSE'])
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->get();

                        $currentAgentCount = QueueLog::whereIn('Event', ['ADDMEMBER'])
                            ->whereDate('time', $date)
                            ->whereTime('time', '>=', $intervals[$i]->format('H:i:s.u'))
                            ->whereTime('time', '<=', $intervals[$i + 1]->format('H:i:s.u'))
                            ->where('queuename', $request->queue)
                            ->distinct('agent')
                            ->count('agent');

                        $agent = $currentAgentCount == 0 ? $lastAgentCount : $currentAgentCount;

                        // Process PAUSE and UNPAUSE events
                        $pauseStartTimes = $carryoverPauseEvents;  // Start with carryover events from the previous interval
                        foreach ($periodData as $pd) {
                            $breakType = $pd->data1;

                            if (in_array($breakType, $breakDurations)) {
                                $eventTime = Carbon::parse($pd->time);

                                if ($pd->Event == 'PAUSE') {
                                    // Store the PAUSE start time for the break type
                                    $pauseStartTimes[$breakType] = $eventTime;
                                } elseif ($pd->Event == 'UNPAUSE' && isset($pauseStartTimes[$breakType])) {
                                    // Calculate the time difference when UNPAUSE event is found for the same break type
                                    $pauseStartTime = $pauseStartTimes[$breakType];
                                    $duration = $eventTime->diffInSeconds($pauseStartTime);

                                    // Add the duration to the total break time
                                    $breakTimeDuration += $duration;

                                    // Reset the PAUSE start time for this break type
                                    unset($pauseStartTimes[$breakType]);
                                }
                            }
                        }

                        // Store the remaining PAUSE events to carry over to the next interval
                        $carryoverPauseEvents = $pauseStartTimes;

                        // Convert seconds to hours
                        $totalBreakMints = $breakTimeDuration / 60;
                        $roundedBreakMints = round($totalBreakMints);
                        $totalBreakHours = $roundedBreakMints / 60;
                        $roundedBreakHours = ceil($totalBreakHours);
                        //$roundedBreakHours = floor($totalBreakMints/60);

                        if ($inboundCalls !== 0) {
                            $acr = round(($abandonCalls / $inboundCalls) * 100);
                        } else {
                            $acr = 0;
                        }

                        $totalData[] = [
                            'from' => $intervals[$i]->format('H:i'),
                            'to' => $to ?? null,
                            'totalInboundCalls' => $inboundCalls,
                            'totalAbandonCalls' => $abandonCalls,
                            'breakTimeDuration' => $breakTimeDuration,
                            'agentBreak' => $roundedBreakHours,
                            'agentLogin' => $agent,
                            'acr' => ($acr > 100) ? '100%' : $acr . '%',
                        ];

                        // Update lastAgentCount for the next iteration
                        $lastAgentCount = $currentAgentCount;

                        // Check if the last agent count should be used
                        $lastAgentData = QueueLog::whereDate('time', $date)
                            ->where('queuename', $request->queue)
                            ->where('Event', 'ADDMEMBER')
                            ->whereNotExists(function ($query) use ($date, $request) {
                                $query->select(DB::raw(1))
                                    ->from('queue_log')
                                    ->whereDate('time', $date)
                                    ->where('queuename', $request->queue)
                                    ->where('Event', 'REMOVEMEMBER');
                            })
                            ->latest('time')
                            ->first();

                        if ($lastAgentData) {
                            $lastAgentCount = QueueLog::whereDate('time', $date)
                                ->where('queuename', $request->queue)
                                ->where('Event', 'ADDMEMBER')
                                ->count('agent');
                        }
                    }
                }
            }

            return response()->json($totalData, 200);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function overallCallHandlingMetric(Request $request)
    {
        $request->validate([
            'queuename' => 'required',
            'month' => 'required_without:day',
            'day' => 'required_without:month'
        ]);
        $threshold = Queue::where('name', $request->queuename)->first();
        if (!$threshold) {
            return response()->json(['message' => 'Queue not found.'], 400);
        }
        $serviceLevel = $threshold->servicelevel ?? 0;
        // filtering data by day and month

        $records = DB::table('queue_log')
            ->where('queuename', $request->queuename)
            ->where(function ($query) use ($request) {
                // filtering by month
                if ($request->has('month')) {
                    $query->where('time', 'LIKE', "{$request->month}%");
                }
                // filtering by day
                if ($request->has('day')) {
                    $query->where('time', 'LIKE', "{$request->day}%");
                }
            })
            ->groupBy('queuename')
            ->select(
                DB::raw("
                queuename as queue,
                COUNT(CASE WHEN (Event = 'ABANDON' OR Event = 'CONNECT' ) THEN 1 END) as totalInboundCalls,
                COUNT(CASE WHEN (Event = 'CONNECT') THEN 1 END) as totalAnswerCalls,
                COUNT(CASE WHEN (Event = 'ABANDON') THEN 1 END) as totalAbandonCalls,
                COUNT(CASE WHEN (Event = 'CONNECT' AND data1 <= $serviceLevel) THEN 1 END) as answeredWithinThreshold,
                COUNT(CASE WHEN (Event = 'CONNECT' AND data1 > $serviceLevel) THEN 1 END) as answeredAfterThreshold,
                COUNT(CASE WHEN ((Event = 'ABANDON')  AND data3 <= $serviceLevel) THEN 1 END) as abandonedCallswithinThreshold,
                COUNT(CASE WHEN ((Event = 'ABANDON')  AND data3 > $serviceLevel) THEN 1 END) as abandonedCallsAfterThreshold
                ")
            )->get();
        return response()->json($records);
    }


    public function getCallBackReport(Request $request)
    {
        $data = DB::table('cdr')->join('queue_log', 'cdr.uniqueid', '=', 'queue_log.callid')
            ->where('cdr.userfield', '0');

        if ($request->has('from') && $request->has('to')) {

            $data->whereBetween(DB::raw('DATE(cdr.start)'), [$request->from, $request->to]);
        }

        if ($request->has('queue')) {
            $data->where('queue_log.queuename', $request->queue);
        }
        return response()->json($data->get(['cdr.src', 'cdr.start', 'queue_log.queuename']));
    }
}
