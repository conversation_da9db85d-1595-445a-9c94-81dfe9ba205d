<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use Carbon\Carbon;
use App\Models\QueueLog;
use App\Models\User;
use DateTime;
use Illuminate\Support\Facades\DB;
use Carbon\CarbonPeriod;
use Carbon\CarbonInterval;
use Symfony\Component\Console\Command\Command as CommandAlias;
use App\Models\MnpReports;

class WorkCodeAgentWiseDailyReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workcode:agent-wise-daily {--queue=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    private function getUsers($start, $end, $queue, $fetchFromQueueLog = true)
    {
        if($fetchFromQueueLog) {
            return QueueLog::query()->select('agent')->where('agent','!=' , 'NONE')->whereDate('time', '>=', $start)->whereDate('time', '<=', $end)->where('queuename', $queue)->get()->pluck('agent')->unique();
        } else {
            return User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name' ,'auth_username'])->whereIn('type', ['Blended','Inbound'])->get();
        }
    }
    public function handle()
    {
        
        $now = now();
        $this->info("[{$now->toDateTimeString()}] INFO: Process started for Work Code Agent Wise Daily Report.");
        $queue = $this->option('queue');
        if(!$queue) {
            $this->error("[{$now->toDateTimeString()}] ERROR: Queue is not mentioned.");
            return CommandAlias::FAILURE;
        }
        $data = [];
        $column = [];
      
        $start = '2023-08-18';
        $end = '2023-08-18';
        // $start = Carbon::yesterday()->toDateString();
        // $end =  Carbon::yesterday()->toDateString();
        $users = $this->getUsers($start,$end, $queue)->toArray();
        $data = DB::table('queue_log')
        ->select('work_codes.name as Workcode', ...array_map(function($user) {
            $escapedUser = "`$user`";
            return DB::raw("SUM(CASE WHEN Agent = '$user' THEN 1 ELSE 0 END) as $escapedUser");
        }, $users))
        ->join('work_codes', 'work_codes.id', '=', 'queue_log.data1')
        ->whereDate('time', '>=', $start)
        ->whereDate('time', '<=', $end)
        ->where('Event', 'WORKCODE')
        ->where('queuename', $queue)
        ->groupBy('Workcode')
        ->get();

        $columns = ['Workcode', ...$users];
        $record = new MnpReports;
        $record->report_name = 'WorkCodeAgentWise';
        $record->report_type = 'Daily';
        $record->report_date =  $start;
        $record->data = json_encode($data);
        $record->save();
        $now = now();
        $this->info("[{$now->toDateTimeString()}] INFO: Process Completed for Work Code Agent Wise Daily Report.");   
        return CommandAlias::SUCCESS;
    }
}
