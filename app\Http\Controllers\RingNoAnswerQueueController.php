<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class RingNoAnswerQueueController extends Controller
{
    public function getData_backup(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.queuename as 'queue', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' group by a.queuename
         */

        $request->validate([
            'queuename' => ['nullable', 'exists:queues,name'],
        ]);

        $date = [];
        $filter = "";

        if (isset($request->time)) {
            $date[0] = Carbon::parse($request->time[0])->toDateTimeString();
            $date[1] = Carbon::parse($request->time[1])->toDateTimeString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
            //            and date(a.time) >= ? and date(a.time) <= ?
        } else {
            $date[0] = Carbon::now()->toDateString();
            $date[1] = Carbon::now()->toDateString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
        }

        if (isset($request->queuename)) {
            $filter .= "and a.queuename = '{$request->queuename}'";
        }

        try {
            $data = DB::select("SELECT a.queuename as 'queue', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' {$filter} group by a.queuename");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getData_BKP(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.queuename as 'queue', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' group by a.queuename
         */

        $request->validate([
            'queuename' => ['nullable', 'exists:queues,name'],
        ]);

        $date = [];
        $filter = "";

        if (isset($request->time)) {
            $date[0] = Carbon::parse($request->time[0])->toDateTimeString();
            $date[1] = Carbon::parse($request->time[1])->toDateTimeString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
            //            and date(a.time) >= ? and date(a.time) <= ?
        } else {
            $date[0] = Carbon::now()->toDateString();
            $date[1] = Carbon::now()->toDateString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
        }

        if (isset($request->queuename) && !empty($request->queuename)) {
            $filter .= "and a.queuename = '{$request->queuename}'";
        } 
        try {
            $data = DB::select("SELECT a.queuename as 'queue', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' {$filter} group by a.queuename");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function getData(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.queuename as 'queue', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' group by a.queuename
         */

        $request->validate([
            'queue' => ['nullable', 'exists:queues,name'],
        ]);

        $filter = "";

        if (isset($request->time) && is_array($request->time)) {
            $start = Carbon::parse($request->time[0])->toDateString();
            $end = Carbon::parse($request->time[1])->toDateString();
        } else {
            $start = Carbon::now()->toDateString();
            $end = $start;
        }

        $filter .= "AND date(a.time) >= '{$start}' AND date(a.time) <= '{$end}'";
        if ($request->filled('queue')) {
            $filter .= " AND a.queuename = '{$request->queue}'";
        }
        try {
            $data = DB::select("SELECT a.queuename as 'queue', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' {$filter} group by a.queuename");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

}
