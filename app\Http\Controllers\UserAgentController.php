<?php

namespace App\Http\Controllers;

use App\Actions\Fortify\PasswordValidationRules;
use App\Models\Role;
use App\Models\User;
use App\Models\UserLimit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\BulkAgentImport;



class UserAgentController extends Controller
{
    use PasswordValidationRules;
    public function __construct()
    {

        $this->middleware('permission:create_agents', ['only' => ['store']]);
        $this->middleware('permission:update_agents', ['only' => ['update']]);
        $this->middleware('permission:delete_agents', ['only' => ['destroy']]);
        $this->middleware('permission:read_agents', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $collection = User::query()->where('type', '!=', 'Normal')->where('type', '!=', 'supervisor')->get();
        $data = $collection->transform(function ($item, $key) {
            $item->queue = $item->queues()->first()->name ?? "";
            return $item;
        });
        return response()->json($data);
    }

    public function index_remove(Request $request): \Illuminate\Http\JsonResponse
    {
        $data = [];

        if ($request->type == 'all') {
            $collection = User::query()->where('type', '!=', 'Normal')->where('type', '!=', 'supervisor')->get();
            $data = $collection->transform(function ($item, $key) {
                $item->queue = $item->queues()->first()->name ?? "";
                return $item;
            });
        } else {
            $collection = User::query()->where('type', '!=', 'Normal')->where('type', '!=', 'supervisor')->with('queues')->get();
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');

            $data = $collection->filter(function ($user) use ($queues) {
                return optional($user->queues->first())->name && in_array($user->queues->first()->name, $queues);
            })->map(function ($user) {
                $user->queue = $user->queues->first()->name;
                unset($user->queues);
                return $user;
            })->values();
        }

        return response()->json($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $agentLimit = UserLimit::first()->agent_limit;
        if (Role::where('name', 'agent')->first()->users()->count() >= $agentLimit) {
            return response()->json("Number of agent limit exceeded.");
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:Inbound,Outbound,Blended'],
            'username' => ['required', 'min:3', 'unique:users'],
            'email' => ['required', 'email', 'unique:users,email', 'regex:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'],
            'password' => ['string', 'min:4', 'confirmed', "required"],
            'auth_username' => ['required', 'min:3', 'string', 'not_in:2', 'unique:users,auth_username'],
            'auth_password' => ['required', 'min:3', 'string', 'not_in:2', 'unique:users,auth_password'],
            'queue' => ['sometimes', 'exists:queues,name'],
        ]);


        try {
            $data = array_merge($request->except('password'), ['password' => Hash::make($request->password)]);
            $user = User::query()->create($data);



            $user->syncRoles(['agent']);

            if ($request->queue) {
                $queue = $request->queue;
                /** @var User $user */
                $user->queues()->attach($queue);
            }
            DB::table('ps_aors')->insert([
                'id' => $request->auth_username,
                'max_contacts' => 1,
                'remove_existing' => 'yes',
                'qualify_frequency' => 1,
            ]);
            DB::table('ps_auths')->insert([
                'id' => $request->auth_username,
                'auth_type' => 'userpass',
                'password' => $request->auth_password,
                'username' => $request->auth_username
            ]);
            DB::table('ps_endpoints')->insert([
                'id' => $request->auth_username,
                'transport' => 'transport-wss',
                'aors' => $request->auth_username,
                'context' => 'default',
                'disallow' => 'all',
                'allow' => 'alaw,ulaw,opus',
                'direct_media' => 'no',
                'webrtc' => 'yes'
            ]);
            return response()->json("Agent {$user->name} has been created.");
        } catch (\Exception $exception) {
            \Log::error("Error occurred: " . $exception->getMessage());
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

    // public function store(Request $request){
    //     return response()->json($request->all());
    // }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, User $user): \Illuminate\Http\JsonResponse
    {

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:Inbound,Outbound,Blended'],
            'username' => ['required', 'min:3', Rule::unique('users')->ignoreModel($user, 'username')],
            'email' => ['required', 'email', 'regex:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/', 'unique:users,email,' . $user->id,],
            //            'password' => [ 'string', 'min:4', 'confirmed' , "required"],
            'auth_username' => ['required', 'min:3', 'numeric', Rule::unique('users')->ignoreModel($user, 'auth_username')],
            'auth_password' => ['required', 'min:3', 'numeric', Rule::unique('users')->ignoreModel($user, 'auth_password')],
            'queue' => ['sometimes', 'exists:queues,name']
        ]);
        try {

            if ($request->type === 'Outbound') {
                $exists = DB::table('queue_user')->where('user_id', $user->id)->exists();
                if ($exists) {
                    DB::table('queue_user')->where('user_id', $user->id)->delete();
                }
            }

            if ($request->exists('queue')) {
                $user->queues()->sync([$request->queue]);
            }
            if ($request->exists('auth_username')) {
                DB::table('ps_endpoints')->where('id', $user->auth_username)->update(['aors' => $request->auth_username, 'id' => $request->auth_username]);
                DB::table('ps_auths')->where('id', $user->auth_username)->update(['username' => $request->auth_username, 'id' => $request->auth_username]);
                DB::table('ps_aors')->where('id', $user->auth_username)->update(['id' => $request->auth_username]);
            }
            if ($request->exists('auth_password')) {
                DB::table('ps_auths')->where('id', $user->auth_username)->update(['password' => $request->auth_password, 'id' => $request->auth_username]);
            }

            $user->update($request->all());
            return response()->json("Agent {$user->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(User $user)
    {
        try {
            DB::beginTransaction();
            DB::table('ps_endpoints')->delete(['id' => $user->auth_username]);
            DB::table('ps_aors')->delete(['id' => $user->auth_username]);
            DB::table('ps_auths')->delete(['id' => $user->auth_username]);
            DB::table('role_user')->where('user_id', $user->id)->delete();
            $user->queues()->detach();
            // $user->detachRoles('agent');
            $user->delete();
            DB::commit();
            return response()->json("Agent {$user->name} has been deleted");
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function bulkAgentImport(Request $request)
    {
        $request->validate([
            'file' => 'file|required'
        ]);

        try {
            $import = new BulkAgentImport();
            $import->import(request()->file('file'));
            if ($import->failures()) {
                $failures = $import->failures();
                return response()->json($failures);
            }
            return response()->json('Succesfully Uploaded');
        } catch (\Exception $exception) {
            // dd('yes');
            // if($exception->failures()){
            //     $failures = $exception->failures();
            //     return response()->json($failures);
            // }
            return response()->json($exception->getMessage(), 500);
        }
    }
}
