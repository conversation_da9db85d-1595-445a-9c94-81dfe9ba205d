<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\{
    SystemSetting, User, PauseReason, WorkCode, Script, QueueLog, CallbackRequest
};
use App\Events\{
    IsReady, AgentLogin, GetPauseReason, GetWorkCode,
    AgentStatus, GetQueues, GetScriptByQueue,
    GetCampagin, GetUserCampagin, GetUser,
    GetSystemSetting, GetAbandonCallReport, GetCallbackRequest
};
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;

class CheckAgentReadyStatus extends Command
{
    protected $signature = 'app:check-agent-ready-status';
    protected $description = 'Real-time Agent Status Tracker';

    protected $client;
    protected $users = [];
    protected $lastBroadcastTime = 0;
    protected $staticDataReloadInterval = 3600;

    public function handle()
    {
        $this->initializeAmiConnection();
        $this->listenForRealTimeEvents();
    }

    protected function initializeAmiConnection()
    {
        $options = $this->getAmiOptions();
        // $options['eventmask'] = 'on'; // all events
        $options['eventmask'] = 'agent,queue'; // Only receive relevant events

        $this->client = new ClientImpl($options);
        $this->client->open();

        // Request initial queue status
        $queueStatus = new QueueStatusAction();
        $this->client->send($queueStatus);

        $this->client->registerEventListener(function (EventMessage $event) {
            $this->handleAmiEvent($event);
        });
    }

    protected function handleAmiEvent(EventMessage $event)
    {
        $eventName = $event->getKey('event');
        Log::debug("AMI Event Received", ['event' => $eventName]);

        // Only process queue member related events
        if (str_starts_with($eventName, 'QueueMember')) {
            $this->handleQueueMemberEvent($event);
        }
    }

    protected function listenForRealTimeEvents()
    {
        while (true) {
            try {
                $this->client->process();
                $this->broadcastUpdatesIfNeeded();
                usleep(100_000);
            } catch (\Exception $e) {
                Log::error("AMI error: " . $e->getMessage());
                $this->reconnectAmi();
            }
        }
    }

    protected function handleQueueMemberEvent(EventMessage $event)
    {
        $keys = $event->getKeys();
        Log::info("Processing queue member event", $keys);

        $interface = $event->getKey('interface');
        if (empty($interface)) {
            Log::warning("No interface found in event");
            return;
        }

        $userId = $this->getUserIdFromAgent($interface);
        if (!$userId) {
            Log::warning("No user found for interface", [
                'interface' => $interface,
                'available_users' => User::pluck('auth_username')->toArray()
            ]);
            return;
        }

        $eventType = $event->getKey('event');
        $isPaused = (int)$event->getKey('paused') === 1;
        $status = $event->getKey('status');

        // Handle different event types
        switch ($eventType) {
            case 'QueueMemberAdded':
                broadcast(new AgentLogin(['user_id' => $userId, 'status' => true]));
                break;

            case 'QueueMemberRemoved':
                broadcast(new AgentLogin(['user_id' => $userId, 'status' => false]));
                break;

            case 'QueueMemberPaused':
                broadcast(new AgentLogin(['user_id' => $userId, 'status' => !$isPaused]));
                break;
        }

        // Always broadcast these status updates
        broadcast(new IsReady(['user_id' => $userId, 'status' => !$isPaused]));
        broadcast(new AgentStatus(['user_id' => $userId, 'status' => $status]));

        Log::info("Processed event for user", [
            'user_id' => $userId,
            'event_type' => $eventType,
            'paused' => $isPaused,
            'status' => $status
        ]);
    }

    protected function broadcastUpdatesIfNeeded()
    {
        $now = time();
        if ($now - $this->lastBroadcastTime < 5) return;

        $this->users = $this->getLoggedInUsers();
        $this->broadcastCommonData();
        $this->lastBroadcastTime = $now;
    }

    protected function broadcastCommonData()
    {
        // $pauseReasons = Cache::remember('pause_reasons', $this->staticDataReloadInterval, fn() => PauseReason::all());
        // $workCodes = Cache::remember('work_codes', $this->staticDataReloadInterval, fn() => WorkCode::all());
        $script = Script::where('status', true)->first();
        $settings = SystemSetting::query()->get();

        foreach ($this->users as $user) {
            broadcast(new GetPauseReason(['user_id' => $user->id, 'data' => PauseReason::all()]));
            broadcast(new GetWorkCode(['user_id' => $user->id, 'data' => WorkCode::all()]));

            if ($user->queues->isNotEmpty()) {
                broadcast(new GetQueues([
                    'user_id' => $user->id,
                    'data' => $user->queues->pluck('name')
                ]));
            }

            if ($script) {
                broadcast(new GetScriptByQueue([
                    'user_id' => $user->id,
                    'queue' => $script->queue_name,
                    'data' => [$script]
                ]));
            }

            broadcast(new GetUser(['user_id' => $user->id, 'data' => $user]));

            // Campaigns
            $campaignData = $user->campaigns()->withCount(['campaign_numbers as count' => function($query) {
                                $query->where('status', false)->where('attempts', '>', 0);
                            }])->get();

            broadcast(new GetUserCampagin(['user_id' => $user->id, 'data' => $campaignData]));

            // Syetem Settings
            $setting=null;
            $myObj = new \stdClass();
            foreach ($settings as $value)
            {
                $id= $value->id;
                $column[]=array('title'=> $value->key, 'dataIndex'=> $value->key, 'key'=> $value->key);
                $key= $value->key;
                $myObj->$key= $value->value;
            }
            $setting= array($myObj);
            $column[] = array('title'=> 'action', 'dataIndex'=> 'action', 'key'=>'action');

            $settingData = ['user_id' => $user->id, 'data' => $setting];

            broadcast(new GetSystemSetting($settingData));


            $this->abandonCallReport($user);
            $this->callbackRequestReport($user);
        }
    }

    protected function reconnectAmi()
    {
        $this->client->close();
        sleep(1);
        $this->initializeAmiConnection();
    }

    protected function getUserIdFromAgent(string $agentName): ?int
    {
        if (strpos($agentName, 'PJSIP/') === false) {
            return User::where('auth_username', $agentName)->value('id');
        }

        return User::where('auth_username', str_replace('PJSIP/', '', $agentName))->value('id');
    }

    protected function getLoggedInUsers()
    {
        $userIds = array_keys(Cache::get('logged_in_users', []));
        return User::whereIn('id', $userIds)->with('queues')->get();
    }

    protected function getAmiOptions(): array
    {
        return Cache::remember('ami_settings', 600, fn() => [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ]);
    }

    public function abandonCallReport($user)
    {
        if($user) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            $pagination = $users[$user->id]['pagination']['abandonCalls'] ?? ['current' => 1, 'pageSize' => 15];
            $current = $pagination['current'];
            $pageSize = $pagination['pageSize'];

            $data = QueueLog::query()->from('queue_log as q')
                    ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                    ->select(
                        DB::raw('DATE(q.time) as date'),
                        DB::raw('TIME(q.time) as time'),
                        'c.uniqueid',
                        'q.callid',
                        'c.src',
                        'c.dst',
                        'q.data3 as waittime',
                        'q.EVENT',
                        'q.status'
                    )
                    ->where('q.EVENT', 'ABANDON')
                    ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                    ->where('q.status', '0');

            $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

            $response = [
                'user_id' => $user->id,
                'current' => $paginatedData->currentPage(),
                'pageSize' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'data' => ['data' => $paginatedData->items()],
            ];

            broadcast(new GetAbandonCallReport($response));
        }
    }

    public function callbackRequestReport($user)
    {
        if($user) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            $pagination = $users[$user->id]['pagination']['callbackRequests'] ?? ['current' => 1, 'pageSize' => 15];
            $current = $pagination['current'];
            $pageSize = $pagination['pageSize'];

            $data = CallbackRequest::query()->from('callback_requests as c')
                    ->select(
                        'c.id',
                        'c.caller_id',
                        'c.queue',
                        'c.request_time',
                        DB::raw('DATE(c.request_time) as date'),
                        DB::raw('TIME(c.request_time) as time'),
                        'c.filePath',
                        'c.fileName',
                        'c.fileLoc'
                    )
                    ->whereDate('c.request_time', '>=', $date1)
                    ->whereDate('c.request_time', '<=', $date2)
                    ->where('c.status', '=', '0');

            $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

            $response = [
                'user_id' => $user->id,
                'currentPage' => $paginatedData->currentPage(),
                'pageSize' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'data' => ['data' => $paginatedData->items()],
            ];

            broadcast(new \App\Events\GetCallbackRequest($response));
        }
    }

    public function __destruct()
    {
        if ($this->client && $this->client->isConnected()) {
            $this->client->close();
        }
    }
}