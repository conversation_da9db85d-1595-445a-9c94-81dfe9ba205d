<?php

namespace App\Http\Controllers;

use App\Models\Queue;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Listener\IEventListener;
use PAMI\Message\Action\AgentsAction;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Event\CoreShowChannelEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;
use App\Models\QueueLog;
use Illuminate\Support\Facades\DB;

class AgentDashboardController extends Controller
{
    /*private function getOptions(): array
    {
        return [
            'host' => '127.0.0.1',
            'scheme' => 'tcp://',
            'port' => '5038',
            'username' => 'defaultapp',
            'secret' => 'randomsecretstring',
            'connect_timeout' => 1000,
            'read_timeout' => 1000
        ];
    }*/

    private function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    private function get_data(string $interface)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new CoreShowChannelsAction();
        try {
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = $response->getEvents();
            $data = [];
            $data['application'] = null;
            $data['connectedlinenum'] = null;
            foreach ($events as $event) {
                if ($event instanceof CoreShowChannelEvent && Str::contains($event->getKey('channel'), $interface)) {
                    $data['application'] = $event->getKey('application');
                    $data['connectedlinenum'] = $event->getKey('connectedlinenum');
                }
            }
            return $data;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    private function parse_time($data)
    {
        return Carbon::createFromTimestamp($data)->format('d-m-Y h:i:s A');
    }

    // private function parse_agent_state(int $state)
    // {
    //     switch ($state) {
    //         default:
    //             return $state;
    //         case 0:
    //             return 'DEVICE_UNKNOWN';
    //         case 1:
    //             return 'DEVICE_NOT_INUSE';
    //         case 2:
    //             return 'DEVICE_INUSE';
    //         case 3:
    //             return 'DEVICE_BUSY';
    //         case 4:
    //             return 'DEVICE_INVALID';
    //         case 5:
    //             return 'DEVICE_UNAVAILABLE';
    //         case 6:
    //             return 'DEVICE_RINGING';
    //         case 7:
    //             return 'DEVICE_RINGINUSE';
    //         case 8:
    //             return 'DEVICE_ONHOLD';

    //     }
    // }

    private function parse_agent_state(int $state)
    {
        switch ($state) {
            default:
                return $state;
            case 0:
                return 'UNKNOWN';
            case 1:
                return 'IDLE'; //'DEVICE_NOT_INUSE'
            case 2:
                return 'ON-CALL'; //'DEVICE_INUSE'
            case 3:
                return 'BUSY';
            case 4:
                return 'INVALID';
            case 5:
                return 'UNAVAILABLE';
            case 6:
                return 'RINGING';
            case 7:
                return 'RINGINUSE';
            case 8:
                return 'ON HOLD';
        }
    }

    public function index(Request $request)
    {

        $queue = $request->queue ?? '100';
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($queue);
        $action2 = new QueueSummaryAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $response2 = $client->send($action2);
            $client->close();
            $events = $response->getEvents();
            $events2 = $response2->getEvents();
            $members = 0;
            $paused = 0;
            $busy = 0;
            $idle = 0;
            $data = [];

            $date = Carbon::now();
            $waitTime = QueueLog::query()
                ->from('queue_log')
                ->whereIn('EVENT', ['CONNECT'])
                ->where('queuename', '=', $queue)
                ->whereDate('time', '>=', $date->startOfDay())
                ->whereDate('time', '<=', $date->endOfDay())
                ->select(DB::raw("SUM(data1) as waittime"))
                ->value('waittime');



            foreach ($events as $key => $event) {
                if ($event instanceof QueueParamsEvent) {
                    $data['queue_params'] = $event->getKeys();
                    $data['queue_params']['totalcalls'] = (int) $event->getKey('completed') + (int) $event->getKey('abandoned');
                    $data['queue_params']['holdtime'] = gmdate("H:i:s", $event->getKey('holdtime'));
                    $data['queue_params']['talktime'] = gmdate("H:i:s", $event->getKey('talktime'));

                    $totalCalls = $data['queue_params']['totalcalls'];
                    $totalTalkTime = $event->getKey('talktime');
                    $totalHoldTime = $event->getKey('holdtime');

                    $data['queue_params']['avgHandlingTime'] = $totalCalls > 0 ? gmdate("H:i:s", ($totalTalkTime + $totalHoldTime) / $totalCalls) : '00:00:00';
                } elseif ($event instanceof QueueMemberEvent) {

                    $members++;

                    if ($event->getKey('paused') == "1") {
                        $paused++;
                    } elseif ($event->getKey('incall') == "1") {
                        $busy++;
                    } else {
                        $idle++;
                    }

                    $data['agents'][$key] = $event->getKeys();
                    $data['agents'][$key]['lastcall'] = $this->parse_time($event->getKey('lastcall'));
                    $data['agents'][$key]['lastpause'] = $this->parse_time($event->getKey('lastpause'));
                    $data['agents'][$key]['status'] = $this->parse_agent_state($event->getKey('status'));
                }
            }

            foreach ($events2 as $event2) {
                if ($event2 instanceof QueueSummaryEvent) {
                    $data['queue_params2'] = $event2->getKeys();
                    $data['queue_params2']['longestholdtime'] = gmdate("H:i:s", $event2->getKey('longestholdtime'));
                    $data['queue_params2']['total'] = $members;
                    $data['queue_params2']['paused'] = $paused;
                    $data['queue_params2']['idle'] = $idle;
                    $data['queue_params2']['busy'] = $busy;
                }
            }

            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function agent_data(Request $request)
    {
        return $this->get_agents_data($request);
    }

    private function get_agents_data(Request $request)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($request->queue);
        try {
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = [];
            $oKey = 0;
            foreach ($response->getEvents() as $key => $event) {
                if ($event instanceof QueueMemberEvent) {
                    $events[$oKey] = $event->getKeys();
                    $events[$oKey]['agentId'] = explode("/", $event->getKey('stateinterface'))[1];
                    $events[$oKey]['lastcall'] = $event->getKey('lastcall') !== "0" ? $this->parse_time($event->getKey('lastcall')) : "N/A";
                    $events[$oKey]['lastpause'] = $event->getKey('lastpause') !== "0" ? $this->parse_time($event->getKey('lastpause')) : "N/A";
                    $events[$oKey]['status'] = $this->parse_agent_state((int) $event->getKey('status'));
                    $events[$oKey]['connected'] = $this->get_data($event->getKey('stateinterface'))["connectedlinenum"];
                    $events[$oKey]['application'] = $this->get_data($event->getKey('stateinterface'))["application"];
                    $oKey++;
                }
            }

            // if ($request->queue) {
            //     cache(['queue' => $request->queue]); // Store the queue without expiration
            // }

            if ($request->userId && $request->queue) {
                $userId = $request->userId;
                cache(["queue_user_{$userId}" => $request->queue]);
            }

            return response()->json($events);
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function getQueues(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(Queue::query()->get()->pluck('name'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
