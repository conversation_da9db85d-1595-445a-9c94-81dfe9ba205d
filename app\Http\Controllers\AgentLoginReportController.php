<?php

namespace App\Http\Controllers;

use App\Models\AgnetReport;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AgentLoginReportController extends Controller
{
    public function formatTimestamp($timestamp)
    {
        $carbonDate = Carbon::parse($timestamp);
        return $carbonDate->format('M j, Y, g:i A');
    }

    public function getAgentLoginReport_backup(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'date' => ['required', 'date_format:Y-m-d']
            ]);

            $loginTime = 0;
            $logoutTime = 0;
            $loginTimeEvent = 0;
            $logoutTimeEvent = 0;
            $data = '';
            $temp = [];

            $agents = AgnetReport::whereDate("time", "=", $request->date)->distinct()->where('Agent', '!=', 'NONE')->when($request->queue, function ($query) use ($request) {
                $query->where('queuename', $request->queue);
            })
                ->pluck('Agent')
                ->toArray();

            $data = DB::table('queue_log')
                ->select('Event', 'time', 'Agent', 'queuename')
                ->whereIn('Agent', $agents)
                ->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER'])
                ->whereDate('time', $request->date)
                ->orderBy('time', 'ASC')
                ->get();

            $agentData = [];
            foreach ($data as $entry) {
                $agentData[$entry->Agent][] = $entry;
            }

            foreach ($agentData as $agentName => $entries) {
                // $agentIds = explode('/', $agentName);
                // $agentCode = $agentIds[1];

                if (strpos($agentName, '/') !== false) {
                    $agentIds = explode('/', $agentName);
                    $agentCode = $agentIds[1];
                } else {
                    $agentCode = $agentName;
                }

                $agentData = User::where('auth_username', '=', $agentCode)->first();

                if ($agentData) {
                    $agentName = $agentData->name;
                }

                foreach ($entries as $i => $entry) {
                    if ($entry->Event === 'ADDMEMBER') {
                        $loginTimeEvent = $this->formatTimestamp($entry->time);
                        $loginTime = $entry->time;
                    } elseif ($entry->Event === 'REMOVEMEMBER' && $loginTime !== null) {
                        $logoutTimeEvent = $this->formatTimestamp($entry->time);
                        $logoutTime = $entry->time;

                        // Check if REMOVEMEMBER time is on the next day
                        if (date('Y-m-d', strtotime($logoutTime)) != date('Y-m-d', strtotime($loginTime))) {
                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => 'Logout on the next day',
                                'time_diff' => ''
                            ];
                        } else {
                            $loginTime = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                            $loginSeconds = round($loginTime);
                            $formattedTimeDiff = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => $logoutTimeEvent,
                                'time_diff' => $formattedTimeDiff
                            ];
                        }

                        // Reset login time after processing a pair
                        $loginTime = null;
                    }
                }

                // Handle case where there's a login without a corresponding logout
                if ($loginTime !== null) {
                    $logoutTime = Carbon::parse($loginTime)->endOfDay();
                    $loginTime = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                    $loginSeconds = round($loginTime);
                    $loginOutput = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                    $logoutTimeEvent = $this->formatTimestamp($logoutTime);

                    $temp[] = [
                        'agent' => $agentName,
                        'login_time' => $loginTimeEvent,
                        'queuename' => $entries[0]->queuename,
                        'logout_time' => $logoutTimeEvent,
                        'time_diff' => $loginOutput
                    ];
                }
            }

            return response()->json($temp);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getAgentLoginReport(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'date' => ['required', 'date_format:Y-m-d']
            ]);

            if(isset($request->queue) && !empty($request->queue)) {
                $queues = [$request->queue];
            }
            else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }


            $loginTime = 0;
            $logoutTime = 0;
            $loginTimeEvent = 0;
            $logoutTimeEvent = 0;
            $data = '';
            $temp = [];

            //            $agents = AgnetReport::whereDate("time", "=", $request->date)->distinct()->where('Agent', '!=', 'NONE')->when($queues, function ($query) use ($queues, $request) {
//                $query->whereIn('queuename', $queues);
//            })->pluck('Agent')->toArray();
            $agents = AgnetReport::whereDate("time", "=", $request->date)->distinct()->where('Agent', '!=', 'NONE')->whereIn('queuename', $queues)->pluck('Agent')->toArray();

            $data = DB::table('queue_log')
                ->select('Event', 'time', 'Agent', 'queuename')
                ->whereIn('Agent', $agents)
                ->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER'])
                ->whereDate('time', $request->date)
                ->orderBy('time', 'ASC')
                ->get();

            $agentData = [];
            foreach ($data as $entry) {
                $agentData[$entry->Agent][] = $entry;
            }

            foreach ($agentData as $agentName => $entries) {
                // $agentIds = explode('/', $agentName);
                // $agentCode = $agentIds[1];

                if (strpos($agentName, '/') !== false) {
                    $agentIds = explode('/', $agentName);
                    $agentCode = $agentIds[1];
                } else {
                    $agentCode = $agentName;
                }

                $agentData = User::where('auth_username', '=', $agentCode)->first();

                if ($agentData) {
                    $agentName = $agentData->name;
                }

                foreach ($entries as $i => $entry) {
                    if ($entry->Event === 'ADDMEMBER') {
                        $loginTimeEvent = $this->formatTimestamp($entry->time);
                        $loginTime = $entry->time;
                    } elseif ($entry->Event === 'REMOVEMEMBER' && $loginTime !== null) {
                        $logoutTimeEvent = $this->formatTimestamp($entry->time);
                        $logoutTime = $entry->time;

                        // Check if REMOVEMEMBER time is on the next day
                        if (date('Y-m-d', strtotime($logoutTime)) != date('Y-m-d', strtotime($loginTime))) {
                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => 'Logout on the next day',
                                'time_diff' => ''
                            ];
                        } else {
                            $loginTime = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                            $loginSeconds = round($loginTime);
                            $formattedTimeDiff = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => $logoutTimeEvent,
                                'time_diff' => $formattedTimeDiff
                            ];
                        }

                        // Reset login time after processing a pair
                        $loginTime = null;
                    }
                }

                // Handle case where there's a login without a corresponding logout
                if ($loginTime !== null) {
                    $logoutTime = Carbon::parse($loginTime)->endOfDay();
                    $loginTime = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                    $loginSeconds = round($loginTime);
                    $loginOutput = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                    $logoutTimeEvent = $this->formatTimestamp($logoutTime);

                    $temp[] = [
                        'agent' => $agentName,
                        'login_time' => $loginTimeEvent,
                        'queuename' => $entries[0]->queuename,
                        'logout_time' => $logoutTimeEvent,
                        'time_diff' => $loginOutput
                    ];
                }
            }

            return response()->json($temp);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function getAgentLoginReportModified(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'date' => ['required', 'date_format:Y-m-d'],
            ]);

            $queues = isset($request->queue) && !empty($request->queue)
                ? [$request->queue]
                : array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');

            $agents = AgnetReport::whereDate('time', $request->date)
                ->where('Agent', "!=", "None")
                ->whereIn('queuename', $queues)
                ->distinct()
                ->pluck('Agent')
                ->toArray();

            $logs = DB::table('queue_log')
                ->select('Event', 'time', 'Agent', 'queuename', 'data1')
                ->whereIn('Agent', $agents)
                ->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE'])
                ->whereDate('time', $request->date)
                ->orderBy('time', 'ASC')
                ->get();

            $groupedData = [];
            foreach ($logs as $entry) {
                $groupedData[$entry->Agent][] = $entry;
            }

            $apiKey = (new TestController())->getApiKey();
            $results = [];

            foreach ($groupedData as $agentRaw => $entries) {
                $loginTime = null;
                $loginTimeEvent = null;
                $logoutTimeEvent = null;

                $agentCode = strpos($agentRaw, '/') !== false ? explode('/', $agentRaw)[1] : $agentRaw;
                $agent = User::where('name', $agentCode)->first();
                $agentId = $agent ? $agent->id : null;

                foreach ($entries as $entry) {
                    $lastEntry = $entry;
                    $status_id = match (true) {
                        $entry->Event === "ADDMEMBER" => 1,
                        $entry->Event === "REMOVEMEMBER" => 2,
                        $entry->Event === "UNPAUSE" => 3,
                        $entry->Event === "PAUSE" && $entry->data1 === "Namaz with lunch break" => 5,
                        $entry->Event === "PAUSE" => 4,
                        default => null
                    };

                    if ($entry->Event === "ADDMEMBER") {
                        $loginTimeEvent = $this->formatTimestamp($entry->time);
                        $loginTime = $entry->time;
                    } elseif ($entry->Event === "REMOVEMEMBER" && $loginTime !== null) {
                        $logoutTimeEvent = $this->formatTimestamp($entry->time);
                        $logoutTime = $entry->time;

                        if (date('Y-m-d', strtotime($logoutTime)) !== date('Y-m-d', strtotime($loginTime))) {
                            $results[] = [
                                'ApiKey' => $apiKey,
                                'AgentID' => $agentId,
                                'StatusID' => $status_id,
                                'TimeIn' => Carbon::parse($loginTimeEvent)->format('Y-m-d H:i:s'),
                                'TimeOut' => 'Logout on the next day'
                            ];
                        } else {
                            $results[] = [
                                'ApiKey' => $apiKey,
                                'AgentID' => $agentId,
                                'StatusID' => $status_id,
                                'TimeIn' => Carbon::parse($loginTimeEvent)->format('Y-m-d H:i:s'),
                                'TimeOut' => Carbon::parse($logoutTimeEvent)->format('Y-m-d H:i:s')
                            ];
                        }

                        $loginTime = null;
                    }
                }

                if ($loginTime !== null) {
                    $endOfDay = Carbon::parse($loginTime)->endOfDay();
                    $logoutTimeEvent = $this->formatTimestamp($endOfDay);

                    $status_id = match (true) {
                        $lastEntry?->Event === "ADDMEMBER" => 1,
                        $lastEntry?->Event === "REMOVEMEMBER" => 2,
                        $lastEntry?->Event === "UNPAUSE" => 3,
                        $lastEntry?->Event === "PAUSE" && $lastEntry?->data1 === "Namaz with lunch break" => 5,
                        $lastEntry?->Event === "PAUSE" => 4,
                        default => 2,
                    };

                    $results[] = [
                        'ApiKey' => $apiKey,
                        'AgentID' => $agentId,
                        'StatusID' => $status_id,
                        'TimeIn' => Carbon::parse($loginTimeEvent)->format('Y-m-d H:i:s'),
                        'TimeOut' => Carbon::parse($logoutTimeEvent)->format('Y-m-d H:i:s')
                    ];
                }
            }

            return response()->json($results);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

}
