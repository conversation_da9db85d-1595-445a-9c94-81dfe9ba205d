<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\CallbackRequest;
use Carbon\Carbon;

class CallbackRequestController extends Controller
{
    public function callbackList() {
        $data = CallbackRequest::all();
        return response()->json([
            'data' => $data
        ], 200);
    }

    public function pendingCallbackList() {
        $data = CallbackRequest::where('status', false)->get();
        return response()->json([
            'data' => $data
        ], 200);
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'userId' => 'required',
            'status' => 'required|boolean',
        ]);

        $callbackRequest = CallbackRequest::findOrFail($id);
        if(!$callbackRequest) {
            return response()->json([
                'message' => 'Callback request not found',
            ], 404);
        }

        $callbackRequest->status = $request->status;
        if ($request->status) {
            $callbackRequest->agent = $request->userId;
            $callbackRequest->answered_date = now();
        }

        $callbackRequest->save();

        return response()->json([
            'message' => 'Callback request status updated successfully!',
            'data' => $callbackRequest
        ], 200);
    }

    public function getCallbackRequest_backup(Request $request)
    {
        $perPage = $request->input('pageSize', 50);
        $currentPage = $request->input('page', 1);

        $query = CallbackRequest::query();

        if ($request->filled('caller_id')) {
            $query->where('caller_id', 'like', '%' . $request->caller_id . '%');
        }

        if ($request->filled('queue')) {
            $query->where('queue', $request->queue);
        }

        if ($request->filled('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        if ($request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();
            $query->whereBetween('request_time', [$startDate, $endDate]);
        }

        $query->leftJoin('users', 'callback_requests.agent', '=', 'users.id')
                ->select([
                    'callback_requests.*',
                    'users.username as agent'
                ]);

        $callbackRequests = $query->orderBy('request_time', 'desc')->paginate($perPage, ['*'], 'page', $currentPage);

        return response()->json([
                'data' => $callbackRequests->items(),
                'current_page' => $callbackRequests->currentPage(),
                'per_page' => $callbackRequests->perPage(),
                'total' => $callbackRequests->total(),
        ]);
    }

    public function getCallbackRequest(Request $request)
    {
        $perPage = $request->input('pageSize', 50);
        $currentPage = $request->input('page', 1);

        $query = CallbackRequest::query();

        if ($request->filled('caller_id')) {
            $query->where('caller_id', 'like', '%' . $request->caller_id . '%');
        }

        if ($request->filled('queue')) {
            $query->where('queue', $request->queue);
        }
        else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            $query->whereIn('queue', $queues);
        }

        if ($request->filled('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        if ($request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();
            $query->whereBetween('request_time', [$startDate, $endDate]);
        }

        $query->leftJoin('users', 'callback_requests.agent', '=', 'users.id')
                ->select([
                    'callback_requests.*',
                    'users.username as agent'
                ]);

        $callbackRequests = $query->orderBy('request_time', 'desc')->paginate($perPage, ['*'], 'page', $currentPage);

        return response()->json([
                'data' => $callbackRequests->items(),
                'current_page' => $callbackRequests->currentPage(),
                'per_page' => $callbackRequests->perPage(),
                'total' => $callbackRequests->total(),
        ]);
    }

    public function export_backup(Request $request)
    {
        $query = CallbackRequest::query();

        if ($request->filled('caller_id')) {
                $query->where('caller_id', 'like', '%' . $request->caller_id . '%');
        }

        if ($request->filled('queue')) {
                $query->where('queue', $request->queue);
        }

        if ($request->filled('status') && $request->status !== '') {
                $query->where('status', $request->status);
        }

        if ($request->filled('start_date') && $request->filled('end_date')) {
                $startDate = Carbon::parse($request->start_date)->startOfDay();
                $endDate = Carbon::parse($request->end_date)->endOfDay();
                $query->whereBetween('request_time', [$startDate, $endDate]);
        }

        $query->leftJoin('users', 'callback_requests.agent', '=', 'users.id')
                ->select([
                    'callback_requests.*',
                    'users.username as agent'
                ]);


        $callbackRequests = $query->orderBy('request_time', 'desc')->get();

        $filename = 'callback_requests_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->stream(function() use ($callbackRequests) {
                $file = fopen('php://output', 'w');

                fwrite($file, "\xEF\xBB\xBF");

                fputcsv($file, [
                   'Caller ID', 'Queue', 'Agent', 'Status', 'Request Time', 'Answered Date'
                ]);

                foreach ($callbackRequests as $request) {
                        fputcsv($file, [
                                $request->caller_id,
                                $request->queue,
                                $request->agent,
                                $request->status ? 'Answered' : 'NotAnswer',
                                $request->request_time,
                                $request->answered_date ?? 'N/A'
                        ]);
                }

                fclose($file);
        }, 200, $headers);
    }

    public function export(Request $request)
    {
        $query = CallbackRequest::query();

        if ($request->filled('caller_id')) {
            $query->where('caller_id', 'like', '%' . $request->caller_id . '%');
        }

        if ($request->filled('queue')) {
            $query->where('queue', $request->queue);
        }
        else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            $query->whereIn('queue', $queues);
        }

        if ($request->filled('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        if ($request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();
            $query->whereBetween('request_time', [$startDate, $endDate]);
        }

        $query->leftJoin('users', 'callback_requests.agent', '=', 'users.id')
                ->select([
                    'callback_requests.*',
                    'users.username as agent'
                ]);


        $callbackRequests = $query->orderBy('request_time', 'desc')->get();

        $filename = 'callback_requests_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->stream(function() use ($callbackRequests) {
                $file = fopen('php://output', 'w');

                fwrite($file, "\xEF\xBB\xBF");

                fputcsv($file, [
                   'Caller Number', 'Queue', 'Agent', 'Status', 'Request Time', 'Answered Date'
                ]);

                foreach ($callbackRequests as $request) {
                        fputcsv($file, [
                                $request->caller_id,
                                $request->queue,
                                $request->agent,
                                $request->status ? 'Completed' : 'Pending',
                                $request->request_time,
                                $request->answered_date ?? 'N/A'
                        ]);
                }

                fclose($file);
        }, 200, $headers);
    }


    public function getCallbackVoicemailAudio(Request $request, $id)
    {
        $callbackRequest = CallbackRequest::findOrFail($id);
        $fileLoc = $callbackRequest->fileLoc;

        return Storage::disk('voicemails')->download($fileLoc);
     }
}
