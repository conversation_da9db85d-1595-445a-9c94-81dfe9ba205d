<?php

namespace App\Console\Commands;

use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueLogAction;
use PAMI\Message\Event\AgentCompleteEvent;
use PAMI\Message\Event\AgentConnectEvent;
use PAMI\Message\Event\DialBeginEvent;
use PAMI\Message\Event\DialEndEvent;
use PAMI\Message\Event\DialEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Event\HangupEvent;
use PAMI\Message\Event\HoldEvent;
use PAMI\Message\Event\QueueMemberAddedEvent;
use PAMI\Message\Event\QueueMemberPauseEvent;
use PAMI\Message\Event\QueueMemberRemovedEvent;
use PAMI\Message\Event\UnholdEvent;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;


class AMIEventsBKP extends Command
{
    /**
     * Get manager options
     * @return array
     */
    public function getManagerOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ami:events';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'The command to watch for AMI events.';

    protected $allowedQueue = '400';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $manager = new ClientImpl($this->getManagerOptions());
        $manager->registerEventListener(function (EventMessage $eventMessage) {
            $this->handleEvent($eventMessage);
            //dump($eventMessage->getKeys());
        }, function (EventMessage $eventMessage) {
            return $this->allowedEvents($eventMessage);
        });
        $manager->open();
        while (true) {
            $manager->process();
        }
    }

    private function allowedEvents(EventMessage $eventMessage): bool
    {
        return (($eventMessage instanceof HoldEvent) ||
            ($eventMessage instanceof UnholdEvent) ||
            ($eventMessage instanceof QueueMemberAddedEvent) ||
            ($eventMessage instanceof QueueMemberRemovedEvent) ||
            ($eventMessage instanceof QueueMemberPauseEvent) ||
            ($eventMessage instanceof AgentConnectEvent) ||
            ($eventMessage instanceof AgentCompleteEvent));
    }

    private function allowedCondition(EventMessage $eventMessage): bool
    {
        // TODO: Only allow specific queue, should be made more concise and clear
        return $eventMessage->getKey('queue') == $this->allowedQueue;
    }

    private function handleEvent(EventMessage $eventMessage)
    {
        $date = Carbon::now();
        try {
            $event = $eventMessage->getKey('event');
            // Log only inbound hold events.
            if ($event === 'Hold') {
                $this->handleHold($eventMessage);
            } elseif ($event === 'Unhold') {
                $this->handleUnhold($eventMessage);
            } elseif ($event === 'QueueMemberAdded') {
                // Handle login event
                $this->sentStatus('Login', $this->getUserIdByAuthUsername(explode('/', $eventMessage->getKey('interface'))[1]));
            } elseif ($event === 'QueueMemberRemoved') {
                // Handle logout event
                $this->sentStatus('Logout', $this->getUserIdByAuthUsername(explode('/', $eventMessage->getKey('interface'))[1]));
            } elseif ($event === 'QueueMemberPause') {
                // Handle pause / unpause event
                $this->sentStatus($eventMessage->getKey('paused') ? 'Pause' : 'Unpause', $this->getUserIdByAuthUsername(explode('/', $eventMessage->getKey('interface'))[1]));
            } elseif ($event === 'AgentConnect') {
                // Handle start call event
                $this->startCall($this->getUserIdByAuthUsername(explode('/', $eventMessage->getKey('interface'))[1]), $eventMessage->getKey('uniqueid'), 'Inbound');
            } elseif ($event === 'AgentComplete') {
                // Handle end call event
                $this->endCall($this->getUserIdByAuthUsername(explode('/', $eventMessage->getKey('interface'))[1]));
            }

        } catch (\Exception $e) {
            //$this->error("{$e->getTraceAsString()}\n");
            $this->info("[{$date->toDateTimeString()}] INFO: Exception occurred: {$e->getMessage()} LINE:{$e->getLine()}");
        }

    }

    private function handleHold(EventMessage $eventMessage)
    {
        if ($eventMessage->getKey('accountcode') !== 'Outbound') {
            $record = $this->getLog($eventMessage);
            $record->Event = "HOLD";
            $record->data1 = $eventMessage->getKey('connectedlinenum');
            $record->save();
        }
        return 0;

    }

    private function handleUnhold(EventMessage $eventMessage)
    {
        if ($eventMessage->getKey('accountcode') !== 'Outbound') {
            $record = $this->getLog($eventMessage);
            $record->Event = "UNHOLD";
            $record->data1 = $eventMessage->getKey('connectedlinenum');
            $record->save();
        }
        return 0;

    }

    /**
     * @param EventMessage $eventMessage
     * @return QueueLog
     */
    private function getLog(EventMessage $eventMessage): QueueLog
    {
        $user = explode('-', explode('/', $eventMessage->getKey('channel'))[1]);
        $user = User::query()->where('auth_username', $user)->first();
        $queue = $user->queues()->first()->name;
        $record = new QueueLog();
        // changed to linkedid because we want to capture hold of agent.
        $record->callid = $eventMessage->getKey('linkedid');
        $record->queuename = $queue;
        $record->Agent = $user->name;
        $record->timestamps = false;
        $record->time = now()->format('Y-m-d H:i:s.u');
        return $record;
    }

    private function getUserIdByAuthUsername($authUsername): int
    {
        return User::query()->where('auth_username', $authUsername)->first('id')->id;
    }



    // public function sentStatus($event, $user_id)
    // {

    //     if ($event === 'Login') {
    //         $status_id = 1;
    //     } elseif ($event === 'Logout') {
    //         $status_id = 2;
    //     } elseif ($event === 'Pause') {
    //         $status_id = 4;
    //     } elseif ($event === 'Unpause') {
    //         $status_id = 3;
    //     }

    //     $result = $this->getApiKey();
    //     if ($result['Response Code'] == 200) {
    //         $api_key = $result['API Key'];
    //         $response = Http::withBasicAuth('MPCCS', 'ieVW0$!5Con9')
    //             ->withoutVerifying()
    //             ->post('https://mnpapp.mulphico.pk/ords/mnpccs/api/agentstatus/', [
    //                 'AGENT_ID' => $user_id,
    //                 'STATUS_ID' => $status_id,
    //                 'AGENT_API_KEY' => $api_key
    //             ]);
    //         $result = $response->json();

    //     }
    //     $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
    //     $this->info(" Response Message: {$result['Response Message']}, Date/Time:{$result['Date/Time']}, Memory Usage: {$mem_usage} ");
    //     unset($result, $response, $api_key);

    //     //$this->info(json_encode($result));

    // }

    // public function startCall($user_id, $unique_id, $call_type)
    // {
    //     $result = $this->getApiKey();
    //     if ($result['Response Code'] == 200) {
    //         $api_key = $result['API Key'];
    //         $response = Http::withBasicAuth('MPCCS', 'ieVW0$!5Con9')->withoutVerifying()->post('https://mnpapp.mulphico.pk/ords/mnpccs/api/startcall/', [
    //             'AGENT_ID' => $user_id,
    //             'LEAD_ID' => $unique_id,
    //             'CALL_TYPE' => $call_type,
    //             'AGENT_API_KEY' => $api_key
    //         ]);
    //         $result = $response->json();
    //         $call_id = $result['Call ID'];
    //         Cache::put($user_id . '_call_id', $call_id);
    //     }
    //     // $this->info(json_encode($result));
    //     $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
    //     $this->info("Call ID: {$result['Call ID']}, Response Message: {$result['Response Message']}, Date/Time:{$result['Call Date/Time']}, Memory Usage: {$mem_usage} ");
    //     unset($result, $response, $api_key, $call_id);

    // }

    // public function endCall($user_id)
    // {
    //     $result = $this->getApiKey();
    //     if ($result['Response Code'] == 200) {
    //         $api_key = $result['API Key'];
    //         $call_id = Cache::pull($user_id . '_call_id');
    //         $response = Http::withBasicAuth('MPCCS', 'ieVW0$!5Con9')->withoutVerifying()->put('https://mnpapp.mulphico.pk/ords/mnpccs/api/endcall/', [
    //             'AGENT_ID' => $user_id,
    //             'CALL_ID' => "$call_id",
    //             'AGENT_API_KEY' => $api_key
    //         ]);
    //         $result = $response->json();
    //     }
    //     //$this->info(json_encode($result));
    //     $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
    //     $this->info(" Call ID: {$result['Call ID']}, Response Message: {$result['Response Message']}, Date/Time:{$result['Call Date/Time']}, Memory Usage: {$mem_usage} ");
    //     unset($result, $response, $api_key, $call_id);

    // }




    public function getAccessToken()
    {
        // Static client credentials
        $clientId = 'm5YqAmnlen9Ld44UItTAJA..';
        $clientSecret = '1xbguNS3EM5ehlzTyZ4stQ..';

        $basicToken = base64_encode($clientId . ':' . $clientSecret);
        $response = Http::withoutVerifying()
            ->withHeaders([
                'Authorization' => 'Basic ' . $basicToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])
            ->asForm()
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/oauth/token', [
                'grant_type' => 'client_credentials',
            ]);
        if ($response->successful()) {
            return $response->json()['access_token'];
        } else {
            // You can log the error for debugging
            \Log::error('Token retrieval failed', ['response' => $response->body()]);
            return null;
        }
    }
    public function getApiKey()
    {
        $token = $this->getAccessToken();
        $response = Http::withoutVerifying()
            ->withToken($token)
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/get_api_key/', [
                'username' => 'MPCCS',
            ]);

        if ($response->successful()) {
            return $response->json(['API Key']);
        } else {
            \Log::error('API Key retrieval failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            return null;
        }
    }

    public function sentStatus()
    {
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();
        $response = Http::withToken($token)->
            withoutVerifying()
            ->post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/agent_login_status', [
                'AgentID' => "146",
                'AgentApiKey' => $api_key
            ]);
        $result = $response->json();

        $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
        $this->info(" Response Message: {$result['Response Message']}, Date/Time:{$result['Date/Time']}, Memory Usage: {$mem_usage} ");
        unset($result, $response, $api_key);
    }

    public function startCall($user_id, $unique_id, $call_type)
    {
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();
        $response = Http::withToken($token)->withoutVerifying()->
            post('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/startcall/', [
                'AgentID' => $user_id,
                'LeadID' => $unique_id,
                'CallType' => $call_type,
                'AgentApiKey' => $api_key,
            ]);
        $result = $response->json();
        $call_id = $result['Call ID'];
        Cache::put($user_id . '_call_id', $call_id);

        // $this->info(json_encode($result));
        $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
        $this->info("Call ID: {$result['Call ID']}, Response Message: {$result['Response Message']}, Date/Time:{$result['Call Date/Time']}, Memory Usage: {$mem_usage} ");
        unset($result, $response, $api_key, $call_id);
    }

    public function endCall($user_id)
    {
        $token = $this->getAccessToken();
        $api_key = $this->getApiKey();
        $call_id = Cache::pull($user_id . '_call_id');
        $response = Http::withToken($token)->withoutVerifying()
            ->put('https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/endcall/', [
                'AgentID' => $user_id,
                'CallID' => "$call_id",
                // 'EndCallFeedback' => $request->EndCallFeedback,
                'AgentApiKey' => $api_key

            ]);
        $result =  $response->json();

        //$this->info(json_encode($result));
        $mem_usage = ((int) (memory_get_usage(true) / (1024 * 1024)));
        $this->info(" Call ID: {$result['Call ID']}, Response Message: {$result['Response Message']}, Date/Time:{$result['Call Date/Time']}, Memory Usage: {$mem_usage} ");
        unset($result, $response, $api_key, $call_id);

    }




}
