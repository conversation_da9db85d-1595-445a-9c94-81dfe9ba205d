<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CallPerAgentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //
    }

    public function getCallPerAgentReport_backup(Request $request)
    {
        try {
            $query = "SELECT IFNULL(b.queue_name, '') AS queue,a.* , Round(SEC_TO_TIME(dur/calls)) avgarage, SEC_TO_TIME(dur) AS dur FROM (
                SELECT u.`id`,auth_username, `accountcode` ,username , SUM(duration) dur,COUNT(*) calls, SEC_TO_TIME(MAX(`duration`)) longest_time, c.`accountcode` AS 'type'
                FROM cdr c INNER JOIN `users` u ON c.`dstchannel` LIKE CONCAT('%', u.`auth_username`, '%') where 1 ";
            $temp = "";
            if(isset($request->agents) && count($request->agents) !== 0 ){
                $count = count($request->agents);
                foreach ($request->agents as $index => $agent) {
                    $agent = trim($agent);

                    if($count == 1 ) $temp .=  "'{$agent}'"; //$agent;
                    else if($count == $index+1) $temp .= "'{$agent}'"; //$agent;
                    else $temp .= "'{$agent}', "; //$agent." , ";
                }
            }

            if(isset($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $query .= "AND date(start) >= '{$start->toDateString()}' AND date(end) <= '{$end->toDateString()}' ";
            }else {
                $query .= " AND MONTH(`start`) = MONTH(CURRENT_DATE()) AND YEAR(`start`) = YEAR(CURRENT_DATE()) ";
            }

            if((isset($request->agents) && count($request->agents) !== 0 )) {
                $query .= "AND u.`name` IN ({$temp})";
            }
            // if(isset($request->range) || (isset($request->agents) && count($request->agents) !== 0 )) {
            //     $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
            //     $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
            //     if (isset($request->range)) $query .= "AND date(start) >= '{$start->toDateString()}' AND date(end) <= '{$end->toDateString()}' ";
            //     if (isset($request->agents) && count($request->agents) !== 0 ) $query .= "AND u.`auth_username` IN ({$temp})";
            // }
            // else $query .= " AND MONTH(`start`) = MONTH(CURRENT_DATE()) AND YEAR(`start`) = YEAR(CURRENT_DATE()) ";
            $query .= "AND `disposition` = 'ANSWERED' GROUP BY u.`auth_username`) as a LEFT JOIN (SELECT * FROM queue_user ) b ON (a.id = b.user_id) ";
            return response()->json(DB::select($query));
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCallPerAgentReport(Request $request)
    {
        try {
            if (isset($request->queue) && !empty($request->queue)) {
                $queueName = trim($request->queue);
                $queuesArr = [$queueName];
            }
            else {
                $queuesArr = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }

	    if (empty($queuesArr)) {
	        return response()->json([]);
	    }
            $queuePlaceholders = implode(',', array_fill(0, count($queuesArr), '?'));

            $query = "SELECT IFNULL(b.queue_name, '') AS queue,a.* , Round(SEC_TO_TIME(dur/calls)) avgarage, SEC_TO_TIME(dur) AS dur FROM (
                SELECT u.`id`,auth_username, `accountcode` ,username , SUM(duration) dur,COUNT(*) calls, SEC_TO_TIME(MAX(`duration`)) longest_time, c.`accountcode` AS 'type'
                FROM cdr c INNER JOIN `users` u ON c.`dstchannel` LIKE CONCAT('%', u.`auth_username`, '%') where 1 ";
            $temp = "";
            if(isset($request->agents) && count($request->agents) !== 0 ){
                $count = count($request->agents);
                foreach ($request->agents as $index => $agent) {
                    $agent = trim($agent);

                    if($count == 1 ) $temp .=  "'{$agent}'"; //$agent;
                    else if($count == $index+1) $temp .= "'{$agent}'"; //$agent;
                    else $temp .= "'{$agent}', "; //$agent." , ";
                }
            }

            if(isset($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $query .= "AND date(start) >= '{$start->toDateString()}' AND date(end) <= '{$end->toDateString()}' ";
            }else {
                $query .= " AND MONTH(`start`) = MONTH(CURRENT_DATE()) AND YEAR(`start`) = YEAR(CURRENT_DATE()) ";
            }

            if((isset($request->agents) && count($request->agents) !== 0 )) {
                $query .= "AND u.`name` IN ({$temp})";
            }
            
            $query .= "AND `disposition` = 'ANSWERED' GROUP BY u.`auth_username`) as a LEFT JOIN (SELECT * FROM queue_user where queue_name IN ($queuePlaceholders) ) b ON (a.id = b.user_id) ";
            $query .= " WHERE b.queue_name IS NOT NULL";
            return response()->json(DB::select($query, $queuesArr));
            
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
