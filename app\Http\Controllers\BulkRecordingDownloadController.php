<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use STS\ZipStream\ZipStream;
use Zip;
use ZipArchive;
use Illuminate\Support\Facades\DB;

class BulkRecordingDownloadController extends Controller
{
    function dateRange($first, $last, $step = '+1 day', $format = 'Y-m-d'): array
    {
        $dates = [];
        $current = strtotime( $first );
        $last = strtotime( $last );
        while( $current <= $last ) {
            $dates[] = date( $format, $current );
            $current = strtotime( $step, $current );
        }
        return $dates;
    }

    public function getBulkData_backup(Request $request)
    {
        if(isset($request->date)) {
            $date = $request->date;
            $current = Carbon::parse($date);
            $year = $current->format('Y');
            $month = $current->format('m');
            $day = $current->format('d');
            $files = Storage::disk('recordings')->files("/{$year}/{$month}/{$day}");

            if (empty($files)) {
                return response()->json(['message' => 'No recordings found for the given date.'], 404);
            }

            $zip = new ZipArchive();
            $zipPath = storage_path('app/recordings.zip');

            if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
                return response()->json("Failed to create ZIP file.", 500);
            }

            foreach ($files as $file) {
                $zip->addFile(
                    Storage::disk('recordings')->path($file),
                    basename($file)
                );
            }

            $zip->close();

            return response()->download($zipPath)->deleteFileAfterSend(true);
        } else {
            return response()->json(['message' => 'Invalid datetime format.'], 404);
        }
    }

    public function getBulkData(Request $request)
    {
        if(isset($request->date)) {

            ini_set('memory_limit', '-1');

            $date = $request->date;
            $current = Carbon::parse($date);
            $year = $current->format('Y');
            $month = $current->format('m');
            $day = $current->format('d');

            if (isset($request->queue) && !empty($request->queue)) {
                $queues = [$request->queue];
            }
            else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }
            $allowedCallIds = DB::table('queue_log')->select('callid')->whereIN('queuename', $queues)->pluck('callid')->toArray();
            
            if(empty($allowedCallIds)) {
                return response()->json(['message' => 'No recordings found for the given date.'], 422);
            }

            $allFiles = Storage::disk('recordings')->files("/{$year}/{$month}/{$day}");

            if (empty($allFiles)) {
                return response()->json(['message' => 'No recordings found for the given date.'], 422);
            }

            $matchedFiles = array_filter($allFiles, function ($file) use ($allowedCallIds) {
                foreach ($allowedCallIds as $callid) {
                    if (str_contains($file, $callid)) {
                        return true;
                    }
                }
                return false;
            });

            if (empty($matchedFiles)) {
                return response()->json(['message' => 'No recordings found for the given date.'], 422);
            }

            $zip = new ZipArchive();
            $zipPath = storage_path('app/recordings.zip');

            if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
                return response()->json("Failed to create ZIP file.", 500);
            }

            foreach ($matchedFiles as $file) {
                $zip->addFile(Storage::disk('recordings')->path($file), basename($file));
            }
            
            $zip->close();

            return response()->download($zipPath)->deleteFileAfterSend(true);
        } else {
            return response()->json(['message' => 'Invalid datetime format.'], 422);
        }
    }

}
