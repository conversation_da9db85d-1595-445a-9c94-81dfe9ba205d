<?php

namespace App\Http\Controllers;

use App\Models\Queue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Helpers\AppHelper;

class QueueController extends Controller
{
    public function __construct()
    {

        $this->middleware('permission:create_queues', ['only' => ['store']]);
        $this->middleware('permission:update_queues', ['only' => ['update']]);
        $this->middleware('permission:delete_queues', ['only' => ['destroy']]);
        $this->middleware('permission:read_queues', ['only' => ['index']]);
    }

    /**
     * Get Enum Values
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnumValues()
    {

        $columns = Schema::getColumnListing('queues');
        $data = [];

        foreach ($columns as $column) {
            $data[$column] = Queue::getPossibleEnumValues($column);
        }

        $EnumColumn = array_filter($data, function ($value) {
            return $value !== [];
        });
        return response()->json(['enum' => $EnumColumn, 'column' => $data]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
//        $queues = AppHelper::role_queues($request);
//        $queueNames = array_column($queues, 'queue_name');
//        return response()->json(Queue::query()->whereIn('name', $queueNames)->get());
	   return response()->json(Queue::query()->get());
    }

    public function getAllQueues(Request $request)
    {
        return response()->json(Queue::query()->get());
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {


        $request->validate([
            'name' => ['required', 'unique:queues,name', 'regex:/^[\w-]*$/']
        ]);

        try {
            $queue = Queue::query()->create($request->all());
            return response()->json("Queue {$queue->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \App\Models\Queue $queue
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Queue $queue)
    {
        $request->validate([
            'name' => ['required', Rule::unique('queues')->ignoreModel($queue, 'name'), 'regex:/^[\w-]*$/']
        ]);

        try {
            $queue->update($request->all());
            return response()->json("Queue {$queue->name} has been updated.");
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     * @param  \App\Models\Queue $queue
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Queue $queue): \Illuminate\Http\JsonResponse
    {
        try {
            $queue->delete();
            return response()->json("Queue {$queue->name} has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }
}
