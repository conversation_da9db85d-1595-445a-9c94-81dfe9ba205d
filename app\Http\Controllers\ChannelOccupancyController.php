<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ChannelOccupancyController extends Controller
{
    public function getData(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT start, COUNT(*) FROM `cdr` where start BETWEEN '2021-07-15 00:00:00' and '2021-07-15 23:59:59' group by TIMESTAMP(start) ORDER BY `start` ASC
         */
        try {
            $date = [];
            if (isset($request->date)) {
                $date[0] = $request->date[0];
                $date[1] = $request->date[1];
            } else {
                $date[0] = Carbon::now()->startOfDay();
                $date[1] = Carbon::now()->endOfDay();
            }
            $data = DB::select("SELECT start as 'datetime', COUNT(*) as 'calls' FROM `cdr` where start BETWEEN '{$date[0]}' and '{$date[1]}' group by TIMESTAMP(start) ORDER BY `start` ASC");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getTrunkPerHourBKP(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            if (isset($request->date)) {
                $date = Carbon::parse($request->date);
            }
            $date = $date->format('Y-m-d');
            $data = DB::select("SELECT CONCAT(CONCAT(EXTRACT(hour FROM time), ':00:00') , '-' , CONCAT((EXTRACT(hour FROM time) + 1), ':00:00')) as 'time_period', SUM(IF(EVENT = 'ABANDON', 1 , 0) + IF(EVENT = 'CONNECT', 1, 0)) AS 'entered' , SUM(IF(EVENT = 'CONNECT', 1, 0)) AS 'answered', SUM(IF(EVENT = 'ABANDON', 1 , 0)) AS 'abandoned' FROM `queue_log` where date(time) = '{$date}' GROUP by HOUR(time)");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getTrunkPerHour(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            if (isset($request->date)) {
                $date = Carbon::parse($request->date);
            }
            $date = $date->format('Y-m-d');

            $queueFilter = "";
            if (isset($request->queue) && !empty($request->queue)) {
                $queueFilter = " AND queuename = '{$request->queue}'";
            } else {
                // $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
                // if (empty($queues)) {
                //     return response()->json(["message" => "No queue found"], 422);
                // }

                // $quotedQueues = "'" . implode("','", $queues) . "'";
                // $queueFilter = " AND queuename IN ({$quotedQueues})";
            }

            // $query = "SELECT 
            //             CONCAT(CONCAT(EXTRACT(hour FROM time), ':00:00') , '-' , 
            //                 CONCAT((EXTRACT(hour FROM time) + 1), ':00:00')) AS 'time_period', 
            //             SUM(IF(EVENT = 'ABANDON', 1 , 0) + IF(EVENT = 'CONNECT', 1, 0)) AS 'entered', 
            //             SUM(IF(EVENT = 'CONNECT', 1, 0)) AS 'answered', 
            //             SUM(IF(EVENT = 'ABANDON', 1 , 0)) AS 'abandoned' 
            //         FROM `queue_log` 
            //         WHERE DATE(time) = '{$date}' 
            //         $queueFilter
            //         GROUP BY HOUR(time)";

            $query = "
                WITH RECURSIVE hours(h) AS (
                SELECT 0 UNION ALL SELECT h+1 FROM hours WHERE h < 23
                )
                SELECT
                CONCAT(LPAD(h,2,'0'), ':00:00-', LPAD(h+1,2,'0'), ':00:00') AS time_period,
                COALESCE(SUM(IF(q.event IN ('ABANDON','CONNECT'),1,0)),0) AS entered,
                COALESCE(SUM(IF(q.event = 'CONNECT',1,0)),0) AS answered,
                COALESCE(SUM(IF(q.event = 'ABANDON',1,0)),0) AS abandoned
                FROM hours
                LEFT JOIN queue_log q
                ON DATE(q.time) = '{$date}' 
                AND HOUR(q.time) = hours.h
                $queueFilter
                GROUP BY hours.h
                ORDER BY hours.h
            ";

            $data = DB::select($query);
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getTrunkPerHourOutbound(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            if (isset($request->date)) {
                $date = Carbon::parse($request->date);
            }
            $date = $date->format('Y-m-d');
            $data = DB::select("SELECT CONCAT(CONCAT(EXTRACT(HOUR FROM START), ':00:00'),'-', CONCAT((EXTRACT(HOUR FROM START) + 1), ':00:00')) AS 'name', SUM(IF(accountcode = 'Outbound', 1, 0)) AS outbound, SUM(IF(accountcode = 'Queue', 1, 0)) AS queue FROM cdr  where date(start) = '{$date}' GROUP by HOUR(start)");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
