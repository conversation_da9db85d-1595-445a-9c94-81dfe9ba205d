<?php

//use App\Http\Controllers\BulkRecordingDownloadController;
use App\Http\Controllers\PrepaidController;
use App\Http\Controllers\TestController;
use App\Models\AgnetReport;
use App\Models\Cdr;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


//Route::get('/getZipFile', [BulkRecordingDownloadController::class, 'getBulkData']);

Route::get('/', function () {
    return view('welcome');
});

Route::middleware(['auth:sanctum', 'verified'])->get('/dashboard', function () {
    return view('dashboard');
})->name('dashboard');

Route::get('test',[\App\Http\Controllers\TestController::class, 'test']);

Route::get('download/recordingFile',[\App\Http\Controllers\RecordingController::class, 'getFiles']);
