<?php

namespace App\Http\Controllers;

use App\Models\Queue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\SummaryReportExport;

class CustomReportController extends Controller
{
    public function queueWiseReport(Request $request)
    {
        $threshold = Queue::first('servicelevel');
        $differnce=0;
        $totalAbd=0;
        $queue_log = DB::table('queue_log')
            ->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
            ->where('queuename', '!=', 'NONE')
            ->groupBy('queuename')
            ->select(
                DB::raw("
                    queuename,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'ABANDON' OR Event ='EXITWITHKEY') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                    /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100,2),0) as CustomerServiceFactor,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                    /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) *100,2),0) as percentageOfAnsweredCalls")
            )->get();
            foreach ($queue_log as  $log) {
                $differnce = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
                $totalAbd=$log->totalAbandonCalls + $differnce;
                $log->totalAbandonCalls =$totalAbd;
            }


        return response()->json($queue_log);
    }

    public function callQueueSummaryReport(Request $request) //
    {
        try {
            $threshold = Queue::first('servicelevel');
            $differnce=0;
            $totalAbd=0;
            $queue = $request->queue ?? 300;
            $cdr = DB::table('queue_log')->where('queuename' , $queue)->where('time', 'LIKE', "{$request->month}%")
                ->groupBy(DB::raw('Date(time)'))
                ->select(
                    DB::raw("
                        Date(time),
                        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                        SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                        SUM(CASE WHEN (Event = 'ABANDON' OR Event ='EXITWITHKEY') THEN 1 ELSE 0 END) as totalAbandonCalls,
                        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) as CustomerServiceFactor,
                        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) *100),0) as percentageOfAnsweredCalls")
                )
                ->get();
                foreach ($cdr as  $log) {
                    $differnce = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
                    $totalAbd=$log->totalAbandonCalls + $differnce;
                    $log->totalAbandonCalls =$totalAbd;
                }


            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function SummaryReport_backup(Request $request){
        $threshold = Queue::first('servicelevel');

        $reports = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
            ->groupBy(DB::raw("DATE(time)"))
            ->select(
                DB::raw("
                    Date(time) as date,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) AS totalCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) AS answerdCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) AS abandonCalls,
                    SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)),1,8) AS averageTalkTime,
                    (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)+SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))/(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)+SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END))*100 AS SLA
                ")
            )->get();

        foreach ($reports as $report ) {
            if($report->averageTalkTime == NUll)
            $report->averageTalkTime= '00:00:00';
            if($report->SLA == NUll)
            $report->SLA= '0.00%';
            else
            $report->SLA = round($report->SLA,2)."%";
        }

        return response()->json($reports);
    }

    public function SummaryReport(Request $request){
        $threshold = Queue::first('servicelevel');

        $query = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to]);

        if (isset($request->queue) && !empty($request->queue)) {
            $queues = [$request->queue];
        }
        else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
        }
        $query->whereIn('queuename', $queues);

        $reports = $query->groupBy(DB::raw("DATE(time)"))
            ->select(
                DB::raw("
                    queuename,
                    Date(time) as date,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) AS totalCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) AS answerdCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) AS abandonCalls,
                    SUM(CASE WHEN (Event IN('ABANDON', 'EXITWITHKEY')) THEN 1 ELSE 0 END) as abandonCalls,
                    SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)),1,8) AS averageTalkTime,
                    (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)+SUM(CASE WHEN (Event IN( 'ABANDON','EXITWITHKEY') AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))/(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)+SUM(CASE WHEN (Event IN( 'ABANDON','EXITWITHKEY')) THEN 1 ELSE 0 END))*100 AS SLA

                ")
            )->get();

        foreach ($reports as $report ) {
            if($report->averageTalkTime == NUll)
            $report->averageTalkTime= '00:00:00';
            if($report->SLA == NUll)
            $report->SLA= '0.00%';
            else
            $report->SLA = round($report->SLA,2)."%";
        }

        return response()->json($reports);
    }

    public function exportSummaryReport_backup(Request $request)
    {
        $from_date=$request->from;
        $to_date = $request->to;

        return Excel::download(new SummaryReportExport($from_date,$to_date), 'summary.csv');
    }

    public function exportSummaryReport(Request $request)
    {
        $from_date=$request->from;
        $to_date = $request->to;

        if (isset($request->queue) && !empty($request->queue)) {
            $queues = [$request->queue];
        }
        else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
        }

        return Excel::download(new SummaryReportExport($from_date, $to_date, $queues), 'summary.csv');
    }
}
