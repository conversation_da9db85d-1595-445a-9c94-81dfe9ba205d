<?php
namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;
use App\Models\EmailSetting;
use App\Models\QueueRole;

class AppHelper
{
    public static function get($key, $default = null)
    {
        if (!Schema::hasTable('email_settings')) {
            return $default;
        }

        return EmailSetting::where('key', $key)->value('value') ?? $default;
    }

    public static function role_queues($request) {
	\Log::info("dddddddddd". json_encode($request->user()));
        $user = $request->user()->load('roles');
        $role = $user->roles[0];
        $queues = QueueRole::where('role_id', $role->id)->get()->toArray();
        return $queues;
    }
}
