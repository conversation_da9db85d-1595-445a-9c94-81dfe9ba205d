<?php

namespace App\Console\Commands;

use App\Http\Controllers\IntegrationMnpApiController;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MnpDailyJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mnp:daily-job';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Call MNP APIs and log login status daily at 2:00 AM';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $mnpController = new IntegrationMnpApiController();

        try {
            $callMnpResponse = $mnpController->callMnpApis();
            Log::info('MNP Daily Job - callMnpApis response:', is_array($callMnpResponse) ? $callMnpResponse : $callMnpResponse->getData(true));
        } catch (\Exception $e) {
            Log::error('MNP Daily Job - callMnpApis failed: ' . $e->getMessage());
        }

        try {
            $loginStatusResponse = $mnpController->loginStatus();
            Log::info('MNP Daily Job - loginStatus response:', is_array($loginStatusResponse) ? $loginStatusResponse : $loginStatusResponse->getData(true));
        } catch (\Exception $e) {
            Log::error('MNP Daily Job - loginStatus failed: ' . $e->getMessage());
        }

        return Command::SUCCESS;
    }
    
}
