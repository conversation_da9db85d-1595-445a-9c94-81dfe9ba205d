<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use App\Services\PromptToSQLService;

class PromptReportController extends Controller
{
    public function generate(Request $request, PromptToSQLService $ai)
    {
        $prompt = $request->input('prompt');

        if (!$prompt) {
            return response()->json(['error' => 'Prompt is required'], 400);
        }

        $sql = $ai->fromPrompt($prompt);

        if (!$sql || !$ai->sanitizeSQL($sql)) {
            return response()->json(['error' => 'Invalid or unsafe SQL generated from prompt.'], 400);
        }

        try {
            $results = DB::select($sql);
        } catch (\Exception $e) {
            return response()->json(['error' => 'SQL Error: ' . $e->getMessage()], 500);
        }

        if ($request->boolean('download')) {
            return $this->downloadCsv($results);
        }

        return view('reports.dynamic', ['data' => $results]);
    }

    private function downloadCsv($data)
    {
        if (empty($data)) {
            return response()->json(['error' => 'No data to download'], 404);
        }

        $filename = 'report_' . now()->format('Ymd_His') . '.csv';
        $handle = fopen('php://temp', 'r+');
        fputcsv($handle, array_keys((array) $data[0]));

        foreach ($data as $row) {
            fputcsv($handle, (array) $row);
        }

        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        return Response::make($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=$filename",
        ]);
    }
}

