<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;


class MnpPharmaApiKey extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api:key';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'gettin api key for mnp pharma integration';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        $response = Http::withBasicAuth('MPCCS', 'ieVW0$!5Con9')->get('http://**************:8080/ords/mnpccs/rest-v1/get_api_key/',[
            'USERNAME' => 'MPCCS'
        ]);
        $result = $response->json();
        $api_key = $result['API Key'];
        Cache::put('api_key', $api_key);

        return 0;
    }
}
