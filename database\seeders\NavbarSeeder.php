<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Navbar;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class NavbarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        DB::table('navbar')->truncate();

        $dashboard = Navbar::create([
            'menu_title' => 'Dashboard',
            'route' => '/',
            'parent_id' => null,
            'permission' => 'view_dashboard',
            'module_accessibility' => true,
        ]);

        $monitoring = Navbar::create([
            'menu_title' => 'Monitoring',
            'route' => '/monitoring',
            'parent_id' => null,
            'permission' => 'view_agentmonitoring',
            'module_accessibility' => true,
        ]);

        $monitoring = Navbar::create([
            'menu_title' => 'Live Monitoring',
            'route' => '/dashboard',
            'parent_id' => null,
            'permission' => 'view_agentLivemonitoring',
            'module_accessibility' => true,
        ]);

        $users = Navbar::create([
            'menu_title' => 'Users',
            'route' => null,
            'parent_id' => null,
            'permission' => 'view_users',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Users',
            'route' => '/users',
            'parent_id' => $users->navbar_id,
            'permission' => 'view_users',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Roles',
            'route' => '/role',
            'parent_id' => $users->navbar_id,
            'permission' => 'view_roles',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Permissions',
            'route' => '/permission',
            'parent_id' => $users->navbar_id,
            'permission' => 'view_permissions',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Agents',
            'route' => '/agent',
            'parent_id' => null,
            'permission' => 'view_agents',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Supervisor',
            'route' => '/supervisor',
            'parent_id' => null,
            'permission' => 'view_supervisors',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Queues',
            'route' => '/queue',
            'parent_id' => null,
            'permission' => 'view_queues',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Campaigns',
            'route' => '/campaign',
            'parent_id' => null,
            'permission' => 'view_campaigns',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'CID Look Up',
            'route' => '/cid-lookup',
            'parent_id' => null,
            'permission' => 'view_cid-lookup',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Forms',
            'route' => '/form',
            'parent_id' => null,
            'permission' => 'view_forms',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Scripts',
            'route' => '/script',
            'parent_id' => null,
            'permission' => 'view_scripts',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Workcodes',
            'route' => '/workCode',
            'parent_id' => null,
            'permission' => 'view_workcodes',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Breaks',
            'route' => '/break',
            'parent_id' => null,
            'permission' => 'view_breaks',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'ContactFlow',
            'route' => '/ivr-flow',
            'parent_id' => null,
            'permission' => 'view_contactflow',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Voicemails',
            'route' => '/voiceMail',
            'parent_id' => null,
            'permission' => 'view_voicemails',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'GreetEase',
            'route' => '/ivr-settings',
            'parent_id' => null,
            'permission' => 'view_greetease',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Service Rating',
            'route' => '/serviceRating',
            'parent_id' => null,
            'permission' => 'view_service-rating',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'DTMF Settings',
            'route' => '/dtmf-settings',
            'parent_id' => null,
            'permission' => 'view_dtmf-settings',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Settings',
            'route' => '/settings',
            'parent_id' => null,
            'permission' => 'view_settings',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Bitrix',
            'route' => '/bitrix',
            'parent_id' => null,
            'permission' => 'view_bitrix',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'PrepaidSetting',
            'route' => '/prepaidSetting',
            'parent_id' => null,
            'permission' => 'view_prepaidSetting',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Ticker',
            'route' => '/ticker',
            'parent_id' => null,
            'permission' => 'view_ticker',
            'module_accessibility' => true,
        ]);

        $email = Navbar::create([
            'menu_title' => 'EmailSetting',
            'route' => '/emailSetting',
            'parent_id' => null,
            'permission' => 'view_emailSetting',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Email Module Accessibility',
            'route' => '/email_module_accessibility',
            'parent_id' => $email->navbar_id,
            'permission' => 'view_emailSetting',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'EmailSetting',
            'route' => '/emailSetting',
            'parent_id' => $email->navbar_id,
            'permission' => 'view_emailSetting',
            'module_accessibility' => true,
        ]);


        $sms = Navbar::create([
            'menu_title' => 'SMS',
            'route' => null,
            'parent_id' => null,
            'permission' => 'view_sms',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Module Accessibility',
            'route' => '/module_accessibility',
            'parent_id' => $sms->navbar_id,
            'permission' => 'view_sms',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'SMS Category',
            'route' => '/sms_category',
            'parent_id' => $sms->navbar_id,
            'permission' => 'view_sms',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'SMS Template',
            'route' => '/sms_template',
            'parent_id' => $sms->navbar_id,
            'permission' => 'view_sms',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Announcement',
            'route' => '/announcements',
            'parent_id' => null,
            'permission' => 'view_announcement',
            'module_accessibility' => true,
        ]);

        $reports = Navbar::create([
            'menu_title' => 'Reports',
            'route' => null,
            'parent_id' => null,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        $agent_perfomance_report = Navbar::create([
            'menu_title' => 'Agent Performance Reports',
            'route' => null,
            'parent_id' => $reports->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        $traffic_analytics_report = Navbar::create([
            'menu_title' => 'Traffic Analytics Reports',
            'route' => null,
            'parent_id' => $reports->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        $call_center_analytics_report = Navbar::create([
            'menu_title' => 'Call Center Analytics Reports',
            'route' => null,
            'parent_id' => $reports->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-a Agent Break Report (Inbound)',
            'route' => '/breakReport',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-b Call Per Agent',
            'route' => '/callPerAgentReport',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-c Daily Login Report',
            'route' => '/dailyLoginReport',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-d Ring No Answer Report',
            'route' => '/RingNoAnswer',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-e Inbound Agent Summary Report',
            'route' => '/inboundAgentSummary',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-f Outbound Agent Summary Report',
            'route' => '/outboundAgentSummary',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '1-g Ring No Answer Agent Wise Summary',
            'route' => '/RingNoAnswerAgentWiseSummaryReport',
            'parent_id' => $agent_perfomance_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '2-a Call Detail Report',
            'route' => '/callDetailReport',
            'parent_id' => $traffic_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '2-b Outbound Activity Report',
            'route' => '/outboundActivity',
            'parent_id' => $traffic_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '2-c Overall Call Handling Metric',
            'route' => '/OverallCallMetric',
            'parent_id' => $traffic_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-a Abandoned Call Report',
            'route' => '/abandonCall',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-b Abandon Call Report Difference Report',
            'route' => '/AbandonCallReportDifference',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-c Inbound Dispostion Report',
            'route' => '/inboundDisposition',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-d Inbound Dispostion Summary',
            'route' => '/inboundDispositionSummary',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-e Ring No Answer Queue Summary Report',
            'route' => '/RingNoAnswerQueueSummaryReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-f Queue Wait Time Report',
            'route' => '/holdTimeReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-g Outbound Dispostion Report',
            'route' => '/outboundDisposition',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-h Outbound Dispostion Summary',
            'route' => '/outboundDispositionSummary',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-i Channel Occupancy graph',
            'route' => '/ChannelOccupancy',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-j Trunk Per Hour Report',
            'route' => '/trunkPerHour',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-k Bulk Recording Download',
            'route' => '/bulkRecordingDownload',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-l Inbound Summary Report',
            'route' => '/summary-report',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-m Callback Request Report',
            'route' => '/callback-report',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-n Merge CDR Form Data Report',
            'route' => '/MergeCDRFormDataReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-o Agent Status Report',
            'route' => '/agentStatus',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-p Hourly Abandon Report',
            'route' => '/hourlyAbandonReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-q Service Level Report',
            'route' => '/serviceLevelReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-r Call Queue Summary - Monthly Report',
            'route' => '/monthlyCallQueue',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-s Queue Wise Report',
            'route' => '/QueueWiseReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-t Work Code Agent Wise Report',
            'route' => '/WorkCodeWiseReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-u Work Code Day Wise Report',
            'route' => '/WorkCodeCountWiseReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-v Agent Wise Report',
            'route' => '/AgentWiseReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => '3-w Abandon Call CLI Details Report',
            'route' => '/CLIAbandonCallsReport',
            'parent_id' => $call_center_analytics_report->navbar_id,
            'permission' => 'view_reports',
            'module_accessibility' => true,
        ]);

        $agentlessCampaign = Navbar::create([
            'menu_title' => 'Agentless Campaign',
            'route' => null,
            'parent_id' => null,
            'permission' => 'view_agentlesscampaign',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Agentless Calling Server',
            'route' => '/servers',
            'parent_id' => $agentlessCampaign->navbar_id,
            'permission' => 'view_callingserver',
            'module_accessibility' => true,
        ]);

        Navbar::create([
            'menu_title' => 'Agentless Recording',
            'route' => '/recordings',
            'parent_id' => $agentlessCampaign->navbar_id,
            'permission' => 'view_agentlessrecording',
            'module_accessibility' => true
        ]);


        Navbar::create([
            'menu_title' => 'Agentless Campaign',
            'route' => '/campaigns',
            'parent_id' => $agentlessCampaign->navbar_id,
            'permission' => 'view_agentcampaign',
            'module_accessibility' => true
        ]);
    }
}
