<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\QueueLog;
use App\Models\User;
use App\Models\WorkCode;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use DateInterval;
use DatePeriod;
use Carbon\CarbonPeriod;

class InboundDispositionSummaryController extends Controller
{
    public function getInboundDispositionSummary_backup(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $cdr = Cdr::query()->where('lastapp', 'Queue')
                ->join('queue_log', 'uniqueid', '=', 'queue_log.callid')
                ->where('Event', 'WORKCODE')->join('work_codes', 'id', '=', 'queue_log.data1')
                ->select('id', 'name as call_status', DB::raw('count(*) as count'))
                ->groupBy('name');
            if ($request->has('date') && is_array($request->date)) {
                $start = Carbon::parse($request->date[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->date[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }
            if ($request->has('queue') && !empty($request->queue)) {
                $cdr->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }
            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getInboundDispositionSummary(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $cdr = Cdr::query()->where('lastapp', 'Queue')
                ->join('queue_log', 'uniqueid', '=', 'queue_log.callid')
                ->where('Event', 'WORKCODE')->where('cdr.disposition','=','ANSWERED')->join('work_codes', 'id', '=', 'queue_log.data1')
                ->select('id', 'name as call_status', DB::raw('count(*) as count'))
                ->groupBy('name');
            if ($request->has('date') && is_array($request->date)) {
                $start = Carbon::parse($request->date[0])->startOfDay();
                $end = Carbon::parse($request->date[1])->endOfDay();
                $cdr->whereBetween("start", [$start, $end]);
            }
            if ($request->has('queue') && !empty($request->queue)) {
                $cdr->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }
            else {
                //$queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
                //$cdr->whereIn('queue_log.queuename', $queues);
            }

            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getWorkcodeAgentWise_backup(Request $request)
    {

        try {
            $data = [];
            $column = [];
            $start = $request->start ?? now()->toDateString();
            $end = $request->end ?? now()->toDateString();
            $queue = $request->queue;
            if ($request->has('agents')) {
                $users = $request->agents;
            } else {
                $users = $this->getUsers($start, $end, $queue)->toArray();
            }
            $data = DB::table('queue_log')
                ->select('work_codes.name as Workcode', ...array_map(function ($user) {
                    $escapedUser = "`$user`";
                    return DB::raw("SUM(CASE WHEN Agent = '$user' THEN 1 ELSE 0 END) as $escapedUser");
                }, $users))
                ->join('work_codes', 'work_codes.id', '=', 'queue_log.data1')
                ->whereDate('time', '>=', $start)
                ->whereDate('time', '<=', $end)
                ->where('Event', 'WORKCODE')
                ->where('queuename', $queue)
                ->groupBy('Workcode')
                ->get();

            $columns = ['Workcode', ...$users];
            return response()->json(['data' => $data, 'columns' => $columns]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getWorkcodeAgentWise(Request $request)
    {
        try {
            $data = [];
            $column = [];
            $start = $request->start ?? now()->toDateString();
            $end = $request->end ?? now()->toDateString();
            
            if ($request->has('queue') && !empty($request->queue)) {
                $queues = [$request->queue];
            }
            else {
                $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
            }

            if ($request->has('agents')) {
                $users = $request->agents;
            } else {
                $users = $this->getUsers($start, $end, $queues)->toArray();
            }
            $data = DB::table('queue_log')
                ->select('work_codes.name as Workcode', ...array_map(function ($user) {
                    $escapedUser = "`$user`";
                    return DB::raw("SUM(CASE WHEN Agent = '$user' THEN 1 ELSE 0 END) as $escapedUser");
                }, $users))
                ->join('work_codes', 'work_codes.id', '=', 'queue_log.data1')
                ->whereDate('time', '>=', $start)
                ->whereDate('time', '<=', $end)
                ->where('Event', 'WORKCODE')
                ->whereIn('queuename', $queues)
                ->groupBy('Workcode')
                ->get();

            $columns = ['Workcode', ...$users];
            return response()->json(['data' => $data, 'columns' => $columns, 'queues'=>$queues]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getWorkcodeDateWise_backup(Request $request)
    // {
    //     $data = [];
    //     $column = [];
    //         $end = $request->end ;
    //         $start = $request->start;
    //         $period = CarbonPeriod::create($start , $end); //for looping over dates
    //         //dd($period);
    //         $dates = CarbonPeriod::create($start , $end)->toArray(); //for column modified format
    //         $dateString = function($dates){
    //             return $dates->toFormattedDateString();
    //         };
    //         $columnDates = array_map($dateString, $dates);
    //         $columns = ['Workcode', ...$columnDates];
    //         $queue_work_code = QueueLog::query()->select('data1')->where('Event', 'WORKCODE')->whereDate('time', '>=', $start)->whereDate('time', '<=', $end)->where('queuename', '300')->get()->pluck('data1')->unique();

    //     $workcodes = WorkCode::query()->select(['id', 'name'])->whereIn('id' ,$queue_work_code)->get()->pluck('name', 'id');
    //     //$workcodes = WorkCode::query()->select(['id', 'name'])->get()->pluck('name', 'id');

    //     foreach ($workcodes as $key => $workcode) {
    //         $datesData = [];
    //         foreach ($period as $dt) {
    //             $modifiedDate = $dt->toDateString();
    //             $datesData[$modifiedDate] =  QueueLog::query()->where('Event', 'WORKCODE')->whereDate('time', '>=', $modifiedDate)->whereDate('time', '<=', $modifiedDate)->where('data1', $key)->count();
    //         }
    //         $data[] = [
    //             'Workcode' => $workcode,
    //             ...$datesData
    //         ];
    //     }
    //     return response()->json(['data' => $data, 'columns' => $columns]);
    // }
    {
        $end = $request->end;
        $start = $request->start;
        $queue = $request->queue;
        //$period = CarbonPeriod::create($start , $end); //for looping over dates
        //dd($period);
        $dates = CarbonPeriod::create($start, $end)->toArray(); //for column modified format
        $dateString = function ($dates) {
            return $dates->toDateString();
        };
        $columnDates = array_map($dateString, $dates);
        $columns = ['Workcode', ...$columnDates];
        $data = DB::table('queue_log')
            ->select('work_codes.name as Workcode', ...array_map(function ($date) {
                return DB::raw("SUM(CASE WHEN DATE(time) = '$date' THEN 1 ELSE 0 END) as '$date'");
            }, $columnDates))
            ->join('work_codes', 'work_codes.id', '=', DB::raw('CAST(queue_log.data1 AS SIGNED)'))
            ->where('Event', 'WORKCODE')
            ->where('queuename', $queue)
            ->where(function ($query) use ($start, $end) {
                $query->whereBetween(DB::raw('DATE(time)'), [$start, $end]);
            })
            ->groupBy('Workcode')
            ->get();

        // Format the result as needed
        $result = [];
        foreach ($data as $row) {
            $workcode = $row->Workcode;
            unset($row->Workcode);
            $result[] = ['Workcode' => $workcode] + (array) $row;
        }

        return response()->json(['data' => $result, 'columns' => $columns]);
    }

    public function getWorkcodeDateWise(Request $request)
    {
        $end = $request->end;
        $start = $request->start;
        
        if ($request->has('queue') && !empty($request->queue)) {
            $queues = [$request->queue];
        }
        else {
            $queues = array_column(\App\Helpers\AppHelper::role_queues($request), 'queue_name');
        }

        $dates = CarbonPeriod::create($start, $end)->toArray(); //for column modified format
        $dateString = function ($dates) {
            return $dates->toDateString();
        };
        $columnDates = array_map($dateString, $dates);
        $columns = ['Workcode', ...$columnDates];
        $data = DB::table('queue_log')
            ->select('work_codes.name as Workcode', ...array_map(function ($date) {
                return DB::raw("SUM(CASE WHEN DATE(time) = '$date' THEN 1 ELSE 0 END) as '$date'");
            }, $columnDates))
            ->join('work_codes', 'work_codes.id', '=', DB::raw('CAST(queue_log.data1 AS SIGNED)'))
            ->where('Event', 'WORKCODE')
            ->whereIn('queuename', $queues)
            ->where(function ($query) use ($start, $end) {
                $query->whereBetween(DB::raw('DATE(time)'), [$start, $end]);
            })
            ->groupBy('Workcode')
            ->get();

        // Format the result as needed
        $result = [];
        foreach ($data as $row) {
            $workcode = $row->Workcode;
            unset($row->Workcode);
            $result[] = ['Workcode' => $workcode] + (array) $row;
        }

        return response()->json(['data' => $result, 'columns' => $columns]);
    }

    private function getUsers_backup($start, $end, $queue, $fetchFromQueueLog = true)
    {
        if ($fetchFromQueueLog) {
            // return DB::select("select DISTINCT Agent as name from queue_log left join users u on u.name = Agent where DATE(time) >= '$start' and DATE(time) <= '$end' and Agent <> 'NONE' group by Agent order by time desc");
            return QueueLog::query()->select('agent')->where('agent', '!=', 'NONE')->whereDate('time', '>=', $start)->whereDate('time', '<=', $end)->where('queuename', $queue)->distinct()->get()->pluck('agent')->unique();
        } else {
            return User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name', 'auth_username'])->whereIn('type', ['Blended', 'Inbound'])->get();
        }
    }

    private function getUsers($start, $end, $queues, $fetchFromQueueLog = true)
    {
        if ($fetchFromQueueLog) {
            return QueueLog::query()->select('agent')->where('agent', '!=', 'NONE')->whereDate('time', '>=', $start)->whereDate('time', '<=', $end)->whereIn('queuename', $queues)->distinct()->get()->pluck('agent')->unique();
        } else {
            return User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name', 'auth_username'])->whereIn('type', ['Blended', 'Inbound'])->get();
        }
    }

    public function getAgentsForFilter(Request $request)
    {

        $agents = DB::select("select DISTINCT Agent as name from queue_log where Agent <> 'NONE' and Agent <> '' AND queuename = '$request->queue' and DATE(time) >= '$request->start' and DATE(time) <= '$request->end' group by Agent order by time desc");
        return response()->json($agents);
    }
}
