<?php
namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class PromptToSQLService
{
    protected $schema;

    public function __construct(SchemaService $schema)
    {
        $this->schema = $schema;
    }

    public function fromPrompt(string $prompt): ?string
    {
        $liveSchema = $this->schema->getFormattedSchema();

        $systemPrompt = <<<PROMPT
You are a SQL assistant for a Laravel app using MySQL.

Rules:
- Do not invent table names. Only use the tables listed below. If a table is not listed, assume it does not exist.
- Only output valid raw MySQL SELECT queries.
- Do not include explanations or markdown.
- Use LEFT(column, 10) for filtering by date if the column is a timestamp.
- Only use the column names listed below.

Tables and columns:
{$liveSchema}
PROMPT;

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('OPENAI_API_KEY', '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'),
        ])->post(env('OPENAI_API_URL', 'https://openrouter.ai/api/v1/chat/completions'), [
            'model' => 'openai/gpt-3.5-turbo',
            'messages' => [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $prompt],
            ],
        ]);

        if (!$response->successful()) {
            \Log::error('OpenAI API failed: ' . $response->body());
            return null;
        }

        $data = $response->json();
        return $data['choices'][0]['message']['content'] ?? null;
    }

    public function validateSQLTables(string $sql): bool
    {
        $tables = DB::select("SHOW TABLES");
        $tableList = array_map(fn($row) => array_values((array)$row)[0], $tables);

        preg_match_all('/from\s+([a-zA-Z0-9_]+)/i', $sql, $matches);
        $usedTables = $matches[1] ?? [];

        foreach ($usedTables as $usedTable) {
            if (!in_array($usedTable, $tableList)) {
                return false;
            }
        }

        return true;
    }

    public function sanitizeSQL(string $sql): ?string
    {
        if (!$this->validateSQLTables($sql)) {
            return null;
        }

        if (!str_starts_with(strtolower(trim($sql)), 'select')) {
            return null;
        }

        return $sql;
    }
}
