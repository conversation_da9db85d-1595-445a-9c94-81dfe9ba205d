<?php

namespace App\Http\Controllers;

use App\Models\MnpReports;
use App\Models\QueueLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class IntegrationMnpApiController extends Controller
{
    //

    public function callMnpApis()
    {
        // $dayEndDate = Carbon::yesterday()->toDateString();
        $dayEndDate ="2025-04-17";
        $clientId = 'ayv_fMGGaAdd5ImhZQQqzw..';
        $clientSecret = 'pCv2xUjGGKdFn0vre1SlpQ..';
        $url = 'https://testmnpapp.mulphico.pk/ords/mnpccs/oauth/token';
        $response = Http::asForm()->withoutVerifying()->withHeaders([
            'Authorization' => 'Basic ' . base64_encode($clientId . ':' . $clientSecret),
        ])->post($url, [
            'grant_type' => 'client_credentials',

        ]);
        if (!$response->successful()) {
            return response()->json(['error' => 'Token request failed'], $response->status());
        }
        $accessToken = $response->json()['access_token'];

        $apiResponse = Http::withoutVerifying()
            ->withToken($accessToken)
            ->get('https://testmnpapp.mulphico.pk/ords/mnpccs/api/get_api_key/', [
                'USERNAME' => 'MPCCS',
            ]);
        $apiKey = $apiResponse->json()['API Key'];
        $url = 'https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/agent_log';
        $rawData = MnpReports::where('report_date', $dayEndDate)
            ->where('report_name', 'Agent Wise Report')
            ->where('report_type', 'Daily')
            ->where('queue', 400)
            ->pluck('data');
        $allAgents = User::get(['name', 'id']);
        $transformedData = [];

        foreach ($rawData as $reportGroup) {
            foreach ($reportGroup as $index => $agent) {

                // Match agent ID from $allAgents
                $matchedAgentId = null;
                foreach ($allAgents as $allAgent) {
                    if (trim($allAgent['name']) === trim($agent['Agent'])) {
                        $matchedAgentId = $allAgent['id'];
                        break;
                    }
                }

                $transformedData[] = [
                    'agentid' => $matchedAgentId ?? '', // set matched ID or leave blank
                    'agentname' => $agent['Agent'] ?? '',
                    'dayenddate' => $dayEndDate,
                    'loggedintime' => $agent['loginTime'] ,
                    'breaktime' => $agent['breakTime'] ,
                    'incomingcalls' => $agent['incomingCalls'] ?? 0,
                    'answeredincomingcalls' => $agent['answeredCalls'] ?? 0,
                    'abandonedincomingcalls' => $agent['abandonedCalls'] ?? 0,
                    'answeredwithinthreshold' => $agent['thresholdCalls'] ?? 0,
                    'outgoingcalls' => $agent['outboundCalls'] ?? 0,
                    'talktimeincoming' => $agent['talkTime'] ,
                    'totalholdtime' => $agent['holdTime'] ,
                    'averagecallhandlingtime' => $agent['AHT'] ,
                    'agentproductivity' => rtrim($agent['agentProductivity'] ?? '0', '%'),
                    'ApiKey' =>  $apiKey
                ];
            }
        }


        $cleanedData = array_filter($transformedData, function ($item) {
            return stripos($item['agentname'], 'total') === false;
        });
        

        // return response()->json($cleanedData);
        $response = Http::withToken($accessToken)->withoutVerifying()
            ->post($url, $cleanedData);
        if ($response->successful()) {
            return $response->json();
        }

        return response()->json([
            'error' => 'Request failed',
            'message' => $response->body(),
            'status' => $response->status()
        ]);
    }
    public function loginStatus()
    {
        ini_set('max_execution_time', -1);
        $dayEndDate = Carbon::yesterday()->toDateString();
        $clientId = 'ayv_fMGGaAdd5ImhZQQqzw..';
        $clientSecret = 'pCv2xUjGGKdFn0vre1SlpQ..';
        $url = 'https://testmnpapp.mulphico.pk/ords/mnpccs/oauth/token';
        $response = Http::asForm()->withoutVerifying()->withHeaders([
            'Authorization' => 'Basic ' . base64_encode($clientId . ':' . $clientSecret),
        ])->post($url, [
            'grant_type' => 'client_credentials',

        ]);
        if (!$response->successful()) {
            return response()->json(['error' => 'Token request failed'], $response->status());
        }
        $accessToken = $response->json()['access_token'];

        $apiResponse = Http::withoutVerifying()
            ->withToken($accessToken)
            ->get('https://testmnpapp.mulphico.pk/ords/mnpccs/api/get_api_key/', [
                'USERNAME' => 'MPCCS',
            ]);
        $apiKey = $apiResponse->json()['API Key'];

        $url = "https://testmnpapp.mulphico.pk/ords/mnpccs/apiv1/agent_log_detail";
        $userMap = User::whereIn('type', ['Blended', 'Inbound'])
            ->pluck('id', 'name') // key = name, value = id
            ->toArray();
        $userNames = array_keys($userMap);


        // Fetch all records in one go
        $allLogs = QueueLog::whereIn('Agent', $userNames)
            ->whereDate('time', $dayEndDate)
            ->whereTime('time', '>=', '00:00')
            ->whereTime('time', '<=', '23:59')
            ->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE', 'HOLD', 'UNHOLD', 'COMPLETECALLER', 'COMPLETEAGENT'])
            ->orderBy('time')
            ->get()
            ->groupBy('Agent'); // Group logs by Agent for fast access

        $data = [];
        $holdData = [];
        $breakData = 0;

        foreach ($userNames as $user) {
            $userId = $userMap[$user] ?? null;
            $records = $allLogs->get($user, collect())->values();


            // Process HOLD/UNHOLD
            for ($y = 0; $y < count($records); $y++) {
                $event = $records[$y]->Event;
                if ($event === 'HOLD' && $y < count($records) - 1) {
                    $nextEvent = $records[$y + 1]->Event;
                    if (in_array($nextEvent, ['UNHOLD', 'REMOVEMEMBER', 'COMPLETEAGENT', 'COMPLETECALLER'])) {
                        $holdData[] = [
                            'AgentID' => "$userId",
                            'TimeIn' => $dayEndDate . ' ' . Carbon::parse($records[$y]->time)->format('H:i:s'),
                            'TimeOut' => $dayEndDate . ' ' . Carbon::parse($records[$y + 1]->time)->format('H:i:s'),
                            'StatusID' => 'On-Hold',
                            'ApiKey' => $apiKey,
                        ];
                    }
                }
            }

            // Process other events
            for ($x = 0; $x < count($records); $x++) {
                $record = $records[$x];
                $event = $record->Event;
                $time = Carbon::parse($record->time)->format('H:i:s');
                $next = $records[$x + 1] ?? null;

                switch ($event) {
                    case 'ADDMEMBER':
                        $data[] = [
                            'AgentID' =>  "$userId",
                            'TimeIn' => $time ? $dayEndDate . ' ' . $time : '',
                            'TimeOut' => '',
                            'StatusID' => '1',
                            'ApiKey' => $apiKey,
                        ];
                        $data[] = [
                            'AgentID' =>  "$userId",
                            'TimeIn' => $time ? $dayEndDate . ' ' . $time : '',
                            'TimeOut' => ($next && in_array($next->Event, ['PAUSE', 'REMOVEMEMBER']))
                                ? $dayEndDate . ' ' . Carbon::parse($next->time)->format('H:i:s')
                                : '',
                            'StatusID' => '3',
                            'ApiKey' => $apiKey,
                        ];
                        break;

                    case 'REMOVEMEMBER':
                        $data[] = [
                            'AgentID' =>  "$userId",
                            'TimeIn' => '',
                            'TimeOut' => $time ? $dayEndDate . ' ' . $time : '',
                            'StatusID' => '2',
                            'ApiKey' => $apiKey,
                        ];
                        break;

                    case 'PAUSE':

                        if ($record->data1 == "Bio Break")
                            $breakData = '5';
                        elseif ($record->data1 == "Counselling/Training")
                            $breakData = '6';
                        elseif ($record->data1 == "Working on CXM")
                            $breakData = '7';
                        elseif ($record->data1 == "Namaz with lunch break")
                            $breakData = '8';
                        elseif ($record->data1 == "Support on HFC Desk")
                            $breakData = '9';
                        elseif ($record->data1 == "Live Debriefing")
                            $breakData = '10';
                        else
                            $breakData = '4';


                        $timeOut = $next && in_array($next->Event, ['UNPAUSE', 'REMOVEMEMBER']) ? Carbon::parse($next->time)->format('H:i:s') : '';
                        $data[] = [
                            'AgentID' =>  "$userId",
                            'TimeIn' => $time ? $dayEndDate . ' ' . $time : '',
                            'TimeOut' => $timeOut ? $dayEndDate . ' ' . $timeOut : '',
                            'StatusID' => $breakData,
                            'ApiKey' => $apiKey,
                        ];
                        break;

                    case 'UNPAUSE':
                        $timeOut = $next && in_array($next->Event, ['PAUSE', 'REMOVEMEMBER']) ? Carbon::parse($next->time)->format('H:i:s') : '';
                        $data[] = [
                            'AgentID' =>  "$userId",
                            'TimeIn' => $time ? $dayEndDate . ' ' . $time : '',
                            'TimeOut' => $timeOut ? $dayEndDate . ' ' . $timeOut : '',
                            'StatusID' => '3',
                            'ApiKey' => $apiKey,
                        ];
                        break;
                }
            }
        }

        $result = array_merge($data, $holdData);
        array_multisort(array_column($result, "ApiKey"), SORT_ASC, $result);
        //return response()->json($result);
        $response = Http::withToken($accessToken)->withoutVerifying()
            ->post($url, $result);
        if ($response->successful()) {
            return $response->json();
        }

        return response()->json([
            'error' => 'Request failed',
            'message' => $response->body(),
            'status' => $response->status()
        ]);
    }
}

