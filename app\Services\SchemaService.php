<?php
namespace App\Services;

use Illuminate\Support\Facades\DB;

class SchemaService
{
    public function getFormattedSchema(): string
    {
        $tables = DB::select("
            SELECT TABLE_NAME, COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            ORDER BY TABLE_NAME, ORDINAL_POSITION
        ");

        $tableColumns = [];
        foreach ($tables as $row) {
            $table = $row->TABLE_NAME;
            $col = $row->COLUMN_NAME;
            $tableColumns[$table][] = $col;
        }

        $lines = [];
        foreach ($tableColumns as $table => $columns) {
            $lines[] = "- {$table}(" . implode(', ', $columns) . ")";
        }

        return implode("\n", $lines);
    }
}

